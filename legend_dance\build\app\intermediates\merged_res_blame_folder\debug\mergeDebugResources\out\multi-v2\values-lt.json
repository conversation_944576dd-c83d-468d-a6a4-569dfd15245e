{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,267,335,416,490,587,682", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "131,195,262,330,411,485,582,677,752"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6450,6531,6595,6662,6730,6811,6885,6982,7077", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "6526,6590,6657,6725,6806,6880,6977,7072,7147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,269,347,493,662,747", "endColumns": "72,90,77,145,168,84,80", "endOffsets": "173,264,342,488,657,742,823"}, "to": {"startLines": "57,112,113,114,117,118,119", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4503,8563,8654,8732,9063,9232,9317", "endColumns": "72,90,77,145,168,84,80", "endOffsets": "4571,8649,8727,8873,9227,9312,9393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,137,221", "endColumns": "81,83,81", "endOffsets": "132,216,298"}, "to": {"startLines": "92,93,94", "startColumns": "4,4,4", "startOffsets": "7205,7287,7371", "endColumns": "81,83,81", "endOffsets": "7282,7366,7448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,298,622,933,1017,1100,1178,1275,1372,1446,1510,1606,1702,1773,1838,1901,1974,2052,2132,2210,2282,2358,2431,2505,2589,2671,2740,2807,2860,2918,2973,3034,3100,3169,3234,3302,3366,3424,3497,3564,3638,3697,3760,3837,3914", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,77,79,77,71,75,72,73,83,81,68,66,52,57,54,60,65,68,64,67,63,57,72,66,73,58,62,76,76,55", "endOffsets": "293,617,928,1012,1095,1173,1270,1367,1441,1505,1601,1697,1768,1833,1896,1969,2047,2127,2205,2277,2353,2426,2500,2584,2666,2735,2802,2855,2913,2968,3029,3095,3164,3229,3297,3361,3419,3492,3559,3633,3692,3755,3832,3909,3965"}, "to": {"startLines": "2,11,17,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,348,672,4576,4660,4743,4821,4918,5015,5089,5153,5249,5345,5416,5481,5544,5617,5695,5775,5853,5925,6001,6074,6148,6232,6314,6383,7152,7453,7511,7566,7627,7693,7762,7827,7895,7959,8017,8090,8157,8231,8290,8353,8430,8507", "endLines": "10,16,22,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,77,79,77,71,75,72,73,83,81,68,66,52,57,54,60,65,68,64,67,63,57,72,66,73,58,62,76,76,55", "endOffsets": "343,667,978,4655,4738,4816,4913,5010,5084,5148,5244,5340,5411,5476,5539,5612,5690,5770,5848,5920,5996,6069,6143,6227,6309,6378,6445,7200,7506,7561,7622,7688,7757,7822,7890,7954,8012,8085,8152,8226,8285,8348,8425,8502,8558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "50,51,52,53,54,55,56,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3752,3850,3960,4059,4162,4273,4383,8962", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3845,3955,4054,4157,4268,4378,4498,9058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "983,1099,1203,1316,1403,1505,1627,1710,1790,1884,1980,2077,2173,2276,2372,2470,2566,2660,2754,2837,2946,3054,3154,3264,3369,3475,3651,8878", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "1094,1198,1311,1398,1500,1622,1705,1785,1879,1975,2072,2168,2271,2367,2465,2561,2655,2749,2832,2941,3049,3149,3259,3364,3470,3646,3747,8957"}}]}]}