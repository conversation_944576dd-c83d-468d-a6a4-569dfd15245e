{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,215", "endColumns": "80,78,79", "endOffsets": "131,210,290"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6868,6949,7028", "endColumns": "80,78,79", "endOffsets": "6944,7023,7103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "708,810,923,1031,1116,1217,1345,1431,1512,1604,1698,1795,1889,1989,2083,2179,2275,2367,2459,2541,2648,2759,2858,2966,3074,3181,3340,8518", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "805,918,1026,1111,1212,1340,1426,1507,1599,1693,1790,1884,1984,2078,2174,2270,2362,2454,2536,2643,2754,2853,2961,3069,3176,3335,3434,8596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,201,266,340,417,484,571,657", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "133,196,261,335,412,479,566,652,721"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6144,6227,6290,6355,6429,6506,6573,6660,6746", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "6222,6285,6350,6424,6501,6568,6655,6741,6810"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,271,352,499,668,756", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "170,266,347,494,663,751,833"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4171,8194,8290,8371,8702,8871,8959", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "4236,8285,8366,8513,8866,8954,9036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3439,3538,3640,3740,3838,3945,4051,8601", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3533,3635,3735,3833,3940,4046,4166,8697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,744,831,916,1012,1108,1183,1251,1346,1441,1507,1576,1642,1713,1790,1865,1941,2011,2098,2168,2248,2340,2431,2497,2561,2614,2672,2720,2779,2844,2906,2972,3044,3108,3169,3235,3300,3366,3419,3484,3563,3642", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,76,74,75,69,86,69,79,91,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,466,653,739,826,911,1007,1103,1178,1246,1341,1436,1502,1571,1637,1708,1785,1860,1936,2006,2093,2163,2243,2335,2426,2492,2556,2609,2667,2715,2774,2839,2901,2967,3039,3103,3164,3230,3295,3361,3414,3479,3558,3637,3695"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,4241,4327,4414,4499,4595,4691,4766,4834,4929,5024,5090,5159,5225,5296,5373,5448,5524,5594,5681,5751,5831,5923,6014,6080,6815,7108,7166,7214,7273,7338,7400,7466,7538,7602,7663,7729,7794,7860,7913,7978,8057,8136", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,76,74,75,69,86,69,79,91,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "330,516,703,4322,4409,4494,4590,4686,4761,4829,4924,5019,5085,5154,5220,5291,5368,5443,5519,5589,5676,5746,5826,5918,6009,6075,6139,6863,7161,7209,7268,7333,7395,7461,7533,7597,7658,7724,7789,7855,7908,7973,8052,8131,8189"}}]}]}