import 'dart:async';
import 'dart:collection';
import 'package:logger/logger.dart';

// 这是一个基于汇编代码反编译的辅助类。
// 原始的performance level可能是一个枚举，这里我们用一个类来复现其结构和数据。
class PerformanceLevel {
  final int index;
  final String name;
  final int targetFps;

  const PerformanceLevel(this.index, this.name, this.targetFps);

  static const VERY_LOW = PerformanceLevel(4, "VERY_LOW", 15);
  static const LOW = PerformanceLevel(3, "LOW", 20);
  static const MEDIUM = PerformanceLevel(2, "MEDIUM", 30);
  static const HIGH = PerformanceLevel(1, "HIGH", 45);
  static const VERY_HIGH = PerformanceLevel(0, "VERY_HIGH", 60);

  static const List<PerformanceLevel> values = [
    VERY_HIGH,
    HIGH,
    MEDIUM,
    LOW,
    VERY_LOW
  ];

  @override
  String toString() {
    return 'PerformanceLevel.$name';
  }
}

class SmartFrameRateController {
  late final Logger logger;

  int targetFps = 30;
  int frameInterval = 33; // 1000ms / 30fps
  int lastFrameTime = 0;
  int processedFrames = 0;
  int skippedFrames = 0;
  
  final List<int> processingTimes = [];
  final List<int> bufferStates = [];

  int consecutiveHighLatencyCount = 0;
  int consecutiveLowLatencyCount = 0;
  
  PerformanceLevel currentLevel = PerformanceLevel.MEDIUM;
  late DateTime lastLevelChangeTime;

  int totalFrames = 0;
  int droppedFrames = 0;
  int levelDowngrades = 0;
  int levelUpgrades = 0;

  bool isInitialized = false;
  Timer? performanceCheckTimer;

  SmartFrameRateController() {
    // 构造函数初始化
    logger = Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: false, // 汇编中传入了一个自定义的格式化函数，但这里logger库没有直接暴露该参数，设为false以简化
      ),
    );
    lastFrameTime = 0;
    processedFrames = 0;
    skippedFrames = 0;
    consecutiveHighLatencyCount = 0;
    consecutiveLowLatencyCount = 0;
    totalFrames = 0;
    droppedFrames = 0;
    levelDowngrades = 0;
    levelUpgrades = 0;
    
    _resetState(); // 构造函数调用了部分重置逻辑
  }

  void dispose() {
    logger.i("SmartFrameRateController: 开始释放资源");
    performanceCheckTimer?.cancel();
    performanceCheckTimer = null;
    isInitialized = false;
    _resetState();
    logger.i("SmartFrameRateController: 资源释放完成");
  }

  void _resetState() {
    targetFps = 30;
    frameInterval = 33;
    lastFrameTime = 0;
    currentLevel = PerformanceLevel.MEDIUM;
    lastLevelChangeTime = DateTime.now();
    processingTimes.clear();
    bufferStates.clear();
    consecutiveHighLatencyCount = 0;
    consecutiveLowLatencyCount = 0;
  }

  Future<void> initialize() async {
    if (isInitialized) {
      logger.w("SmartFrameRateController: 控制器已初始化");
      return;
    }
    try {
      logger.i("SmartFrameRateController: 开始初始化智能帧率控制器");
      _resetState();
      _startPerformanceMonitoring();
      isInitialized = true;
      logger.i("SmartFrameRateController: 智能帧率控制器初始化完成，目标帧率: ${targetFps}fps");
    } catch (e, stackTrace) {
      logger.e("SmartFrameRateController: 初始化失败", error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  void _startPerformanceMonitoring() {
    performanceCheckTimer = Timer.periodic(const Duration(seconds: 1), (Timer timer) {
      _performPeriodicCheck();
    });
  }

  void _performPeriodicCheck() {
    final stats = getPerformanceStats();
    logger.d(
        "SmartFrameRateController: 性能检查 - ${stats['currentLevel']}, 平均处理时间: ${stats['averageProcessingTime']}ms, 丢帧率: ${stats['dropRate']}%");
    _autoAdjustPerformance();
  }

  void _autoAdjustPerformance() {
    double avgProcessingTime = _getAverageProcessingTime();
    double avgBufferState = _getAverageBufferState();

    // 紧急情况：缓冲区严重阻塞或处理时间超长
    if (avgBufferState >= 8.0 || avgProcessingTime > 66.0) {
      logger.w("SmartFrameRateController: 检测到紧急情况，立即降级");
      _forceEmergencyDowngrade();
      return;
    }

    // 考虑降级：缓冲区较大或处理时间较长
    if (avgBufferState > 6.0 || avgProcessingTime > 49.5) {
      _considerDowngrade();
      return;
    }
    
    // 计算平均处理时间/目标间隔时间的比率
    double avgProcessingTimeRatio = 0.0;
    if (totalFrames > 0) {
        // 这部分逻辑在汇编中存在，但似乎没有直接使用其结果来做判断
        // 而是使用了其它条件，此处保留其计算逻辑
        avgProcessingTimeRatio = droppedFrames / totalFrames;
    }

    // 考虑升级：需满足多个条件
    // 1. 平均处理时间远低于帧间隔 (小于16.5ms)
    // 2. 缓冲区健康 (小于1.8)
    // 3. 丢帧率低 (小于10%)
    if (avgProcessingTime < 16.5 && avgBufferState < 1.8 && (avgProcessingTimeRatio < 0.1)) {
        _considerUpgrade();
    }
  }

  void _considerUpgrade() {
      int nextLevelIndex = currentLevel.index - 1;
      if (nextLevelIndex >= 0) {
          PerformanceLevel nextLevel = PerformanceLevel.values[nextLevelIndex];
          if (_canUpgrade()) {
              logger.i("SmartFrameRateController: 性能良好，升级到 ${nextLevel.name}");
              _applyPerformanceLevel(nextLevel);
          }
      }
  }

  bool _canUpgrade() {
    Duration timeSinceLastChange = DateTime.now().difference(lastLevelChangeTime);
    if (timeSinceLastChange.inSeconds >= 10) {
        return _getAverageProcessingTime() < 26.4;
    }
    return false;
  }
  
  void _considerDowngrade() {
      int nextLevelIndex = currentLevel.index + 1;
      if (nextLevelIndex < PerformanceLevel.values.length) {
          PerformanceLevel nextLevel = PerformanceLevel.values[nextLevelIndex];
          if (_canDowngrade()) {
              logger.w("SmartFrameRateController: 性能压力过大，降级到 ${nextLevel.name}");
              _applyPerformanceLevel(nextLevel);
          }
      }
  }

  bool _canDowngrade() {
    Duration timeSinceLastChange = DateTime.now().difference(lastLevelChangeTime);
    return timeSinceLastChange.inSeconds >= 3;
  }

  void _forceEmergencyDowngrade() {
    _applyPerformanceLevel(PerformanceLevel.VERY_LOW);
  }

  void _applyPerformanceLevel(PerformanceLevel newLevel) {
    PerformanceLevel oldLevel = currentLevel;
    currentLevel = newLevel;
    targetFps = newLevel.targetFps;
    if (targetFps > 0) {
        frameInterval = 1000 ~/ targetFps;
    }
    lastLevelChangeTime = DateTime.now();
    consecutiveHighLatencyCount = 0;
    consecutiveLowLatencyCount = 0;

    if (newLevel.index > oldLevel.index) { // index越大，性能越低
        levelDowngrades++;
    } else if (newLevel.index < oldLevel.index) {
        levelUpgrades++;
    }

    logger.i("SmartFrameRateController: 性能级别变更: ${oldLevel.name} → ${newLevel.name} (${newLevel.targetFps}fps)");
  }

  double _getAverageBufferState() {
    if (bufferStates.isEmpty) {
      return 0.0;
    }
    int sum = bufferStates.reduce((a, b) => a + b);
    return sum / bufferStates.length;
  }

  double _getAverageProcessingTime() {
    if (processingTimes.isEmpty) {
      return 0.0;
    }
    int sum = processingTimes.reduce((a, b) => a + b);
    return sum / processingTimes.length;
  }

  Map<String, dynamic> getPerformanceStats() {
    double dropRateValue = (totalFrames > 0) ? (droppedFrames * 100.0 / totalFrames) : 0.0;
    String dropRate = dropRateValue.toStringAsFixed(1);
    
    return {
      'currentFps': targetFps,
      'currentLevel': currentLevel.name,
      'processedFrames': processedFrames,
      'skippedFrames': skippedFrames,
      'totalFrames': totalFrames,
      'droppedFrames': droppedFrames,
      'dropRate': dropRate,
      'averageProcessingTime': _getAverageProcessingTime(),
      'levelDowngrades': levelDowngrades,
      'levelUpgrades': levelUpgrades,
      'averageBufferState': _getAverageBufferState(),
    };
  }
  
  void recordProcessingTime(int time) {
    if (!isInitialized) return;
    processingTimes.add(time);
    if (processingTimes.length > 30) {
        processingTimes.removeAt(0);
    }
    _analyzePerformanceTrend(time);
  }
  
  void _analyzePerformanceTrend(int processingTime) {
      if (processingTime > frameInterval) {
          consecutiveHighLatencyCount++;
          consecutiveLowLatencyCount = 0;
          if (consecutiveHighLatencyCount >= 3) {
              _considerDowngrade();
          }
      } else {
          consecutiveLowLatencyCount++;
          consecutiveHighLatencyCount = 0;
          if (consecutiveLowLatencyCount >= 10) {
              _considerUpgrade();
          }
      }
  }

  void recordBufferState(int bufferSize) {
    if (!isInitialized) return;
    bufferStates.add(bufferSize);
    if (bufferStates.length > 30) {
        bufferStates.removeAt(0);
    }
    _checkBufferPressure(bufferSize);
  }

  void _checkBufferPressure(int bufferSize) {
      if (bufferSize >= 8) {
          logger.e("SmartFrameRateController: 缓冲区压力严重 (${bufferSize}/16)，强制降级");
          _forceEmergencyDowngrade();
      } else if (bufferSize >= 6) {
          logger.w("SmartFrameRateController: 缓冲区压力警告 (${bufferSize}/12)");
          _considerDowngrade();
      }
  }

  bool shouldProcessFrame() {
    if (!isInitialized) {
      return true;
    }

    int now = DateTime.now().microsecondsSinceEpoch;

    if (lastFrameTime == 0) {
      lastFrameTime = now ~/ 1000;
      totalFrames++;
      processedFrames++;
      return true;
    }
    
    double avgBufferState = _getAverageBufferState();
    if(avgBufferState >= 8.0) {
        skippedFrames++;
        droppedFrames++;
        logger.w("SmartFrameRateController: 缓冲区压力过大(${avgBufferState})，强制跳帧");
        return false;
    }

    int nowMillis = now ~/ 1000;
    int elapsed = nowMillis - lastFrameTime;

    if (elapsed >= frameInterval) {
      lastFrameTime = nowMillis;
      processedFrames++;
      totalFrames++;
      skippedFrames = 0;
      return true;
    } else {
        // 强制处理逻辑：为了防止完全卡死，在连续跳过一定数量的帧后，强制处理一帧。
        if (skippedFrames > 0 && skippedFrames % 5 == 0) {
            logger.d("SmartFrameRateController: 连续跳帧过多，强制处理一帧");
            lastFrameTime = nowMillis;
            processedFrames++;
            totalFrames++;
            skippedFrames = 0; // 重置跳帧计数器
            return true;
        }

        skippedFrames++;
        droppedFrames++;
        return false;
    }
  }
}

