{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3366,3462,3564,3661,3759,3866,3975,8463", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3457,3559,3656,3754,3861,3970,4088,8559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,664,750,837,912,997,1084,1155,1219,1317,1413,1485,1550,1616,1686,1762,1839,1913,1986,2066,2142,2211,2295,2378,2441,2509,2562,2620,2668,2729,2799,2871,2939,3013,3077,3136,3200,3270,3336,3388,3449,3525,3600", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,75,76,73,72,79,75,68,83,82,62,67,52,57,47,60,69,71,67,73,63,58,63,69,65,51,60,75,74,52", "endOffsets": "280,469,659,745,832,907,992,1079,1150,1214,1312,1408,1480,1545,1611,1681,1757,1834,1908,1981,2061,2137,2206,2290,2373,2436,2504,2557,2615,2663,2724,2794,2866,2934,3008,3072,3131,3195,3265,3331,3383,3444,3520,3595,3648"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,524,4163,4249,4336,4411,4496,4583,4654,4718,4816,4912,4984,5049,5115,5185,5261,5338,5412,5485,5565,5641,5710,5794,5877,5940,6685,6977,7035,7083,7144,7214,7286,7354,7428,7492,7551,7615,7685,7751,7803,7864,7940,8015", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,75,76,73,72,79,75,68,83,82,62,67,52,57,47,60,69,71,67,73,63,58,63,69,65,51,60,75,74,52", "endOffsets": "330,519,709,4244,4331,4406,4491,4578,4649,4713,4811,4907,4979,5044,5110,5180,5256,5333,5407,5480,5560,5636,5705,5789,5872,5935,6003,6733,7030,7078,7139,7209,7281,7349,7423,7487,7546,7610,7680,7746,7798,7859,7935,8010,8063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "714,814,908,1024,1109,1209,1322,1400,1476,1567,1660,1753,1847,1941,2034,2129,2227,2318,2409,2488,2596,2703,2799,2912,3015,3116,3269,8383", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "809,903,1019,1104,1204,1317,1395,1471,1562,1655,1748,1842,1936,2029,2124,2222,2313,2404,2483,2591,2698,2794,2907,3010,3111,3264,3361,8458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,216", "endColumns": "79,80,77", "endOffsets": "130,211,289"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6738,6818,6899", "endColumns": "79,80,77", "endOffsets": "6813,6894,6972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,189,253,322,399,473,573,664", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "122,184,248,317,394,468,568,659,727"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6008,6080,6142,6206,6275,6352,6426,6526,6617", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "6075,6137,6201,6270,6347,6421,6521,6612,6680"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,342,490,659,739", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "170,258,337,485,654,734,811"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4093,8068,8156,8235,8564,8733,8813", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "4158,8151,8230,8378,8728,8808,8885"}}]}]}