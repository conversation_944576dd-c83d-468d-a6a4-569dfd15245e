-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:1:1-46:12
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:1:1-46:12
	package
		INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:3:5-67
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:3:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:4:5-79
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:4:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:5:5-76
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:5:22-73
queries
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:8:5-10:15
package#com.tencent.mm
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:9:9-50
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:9:18-47
application
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:12:5-45:19
activity#com.jarvan.fluwx.wxapi.FluwxWXEntryActivity
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:14:9-19:58
	android:launchMode
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:17:13-44
	android:exported
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:19:13-55
	android:taskAffinity
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:18:13-52
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:15:13-55
activity-alias#${applicationId}.wxapi.WXEntryActivity
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:21:9-27:58
	android:launchMode
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:24:13-43
	android:exported
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:23:13-36
	android:targetActivity
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:25:13-81
	android:theme
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:27:13-55
	android:taskAffinity
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:26:13-52
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:22:13-66
activity-alias#${applicationId}.wxapi.WXPayEntryActivity
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:29:9-34:58
	android:launchMode
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:32:13-48
	android:exported
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:31:13-36
	android:targetActivity
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:33:13-81
	android:theme
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:34:13-55
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:30:13-69
provider#com.jarvan.fluwx.FluwxFileProvider
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:36:9-44:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:40:13-47
	android:authorities
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:38:13-65
	android:exported
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:39:13-37
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:37:13-62
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:41:13-43:69
	android:resource
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:43:17-66
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:42:17-67
uses-sdk
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml
