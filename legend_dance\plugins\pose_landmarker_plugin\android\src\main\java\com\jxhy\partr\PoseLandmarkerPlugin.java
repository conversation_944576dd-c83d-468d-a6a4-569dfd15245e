package com.jxhy.partr;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Display;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.mediapipe.framework.MediaPipeException;
import com.google.mediapipe.framework.image.BitmapImageBuilder;
import com.google.mediapipe.framework.image.MPImage;
import com.google.mediapipe.tasks.components.containers.NormalizedLandmark;
import com.google.mediapipe.tasks.core.BaseOptions;
import com.google.mediapipe.tasks.core.Delegate;
import com.google.mediapipe.tasks.core.OutputHandler;
import com.google.mediapipe.tasks.core.TaskResult;
import com.google.mediapipe.tasks.vision.core.RunningMode;
import com.google.mediapipe.tasks.vision.poselandmarker.PoseLandmarker;
import com.google.mediapipe.tasks.vision.poselandmarker.PoseLandmarkerResult;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

public class PoseLandmarkerPlugin implements FlutterPlugin, MethodCallHandler, ActivityAware {
    public static final String CHANNEL_NAME = "pose_landmarker_plugin";
    public static final String TAG = "PoseLandmarker";
    private static final long SAVE_INTERVAL = 10000; // 保存间隔

    private MethodChannel channel;
    private Context context;
    private Activity activity;
    private PoseLandmarker poseLandmarker;
    private long lastSaveTime;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        Log.i(TAG, "插件开始初始化");
        
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), CHANNEL_NAME);
        channel.setMethodCallHandler(this);
        context = flutterPluginBinding.getApplicationContext();
        
        Log.i(TAG, "插件初始化完成");
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        Log.d(TAG, "Method called: " + call.method);
        
        switch (call.method) {
            case "initialize":
                initialize(call, result);
                break;
            case "detect":
                try {
                    detect(call, result);
                } catch (Exception e) {
                    String errorMsg = "YUV图像检测失败: " + e.getMessage();
                    Log.e(TAG, errorMsg, e);
                    result.error("DETECT_ERROR", errorMsg, null);
                }
                break;
            case "detectRGBA":
                try {
                    detectRGBA(call, result);
                } catch (Exception e) {
                    String errorMsg = "RGBA图像检测失败: " + e.getMessage();
                    Log.e(TAG, errorMsg, e);
                    result.error("DETECT_RGBA_ERROR", errorMsg, null);
                }
                break;
            default:
                result.notImplemented();
        }
    }

    private void initialize(MethodCall call, Result result) throws MediaPipeException {
        try {
            Log.i(TAG, "开始初始化姿势检测器");
            
            Integer maxPosesArg = call.argument("maxPoses");
            int maxPoses = maxPosesArg != null ? maxPosesArg : 1;
            
            // 释放现有的检测器
            if (poseLandmarker != null) {
                poseLandmarker.close();
            }
            poseLandmarker = null;
            
            // 直接使用assets中的模型文件
            
            // 构建PoseLandmarker选项
            PoseLandmarker.PoseLandmarkerOptions options = 
                PoseLandmarker.PoseLandmarkerOptions.builder()
                    .setBaseOptions(BaseOptions.builder()
                        .setDelegate(Delegate.GPU)
                        .setModelAssetPath("pose_landmarker_lite.task")
                        .build())
                    .setRunningMode(RunningMode.LIVE_STREAM)
                    .setNumPoses(maxPoses)
                    .setResultListener(new OutputHandler.ResultListener<PoseLandmarkerResult, MPImage>() {
                        @Override
                        public void run(PoseLandmarkerResult poseLandmarkerResult, MPImage image) {
                            handleResult(poseLandmarkerResult);
                        }
                    })
                    .build();
            
            // 创建PoseLandmarker
            poseLandmarker = PoseLandmarker.createFromOptions(context, options);
            
            Log.i(TAG, "姿势检测器初始化成功,最大检测人数: " + maxPoses);
            result.success(null);
            
        } catch (Exception e) {
            String errorMsg = "姿势检测器初始化失败: " + e.getMessage();
            Log.e(TAG, errorMsg, e);
            result.error("INITIALIZE_ERROR", errorMsg, null);
        }
    }

    private void detect(MethodCall call, Result result) throws Exception {
        try {
            if (poseLandmarker == null) {
                throw new Exception("请先调用 initialize 方法初始化姿势检测器");
            }
            
            Integer width = call.argument("width");
            Integer height = call.argument("height");
            List<Map<String, Object>> planes = call.argument("planes");
            Integer sensorOrientation = call.argument("sensorOrientation");
            Boolean isFront = call.argument("isFront");
            
            if (width == null) {
                throw new Exception("缺少图像宽度参数");
            }
            if (height == null) {
                throw new Exception("缺少图像高度参数");
            }
            if (planes == null) {
                throw new Exception("缺少图像平面数据");
            }
            if (planes.size() != 3) {
                throw new Exception("图像平面数据格式错误,需要 3 个平面(Y/U/V),实际收到 " + planes.size() + " 个平面");
            }
            if (sensorOrientation == null) {
                throw new Exception("缺少传感器方向参数");
            }
            if (isFront == null) {
                throw new Exception("缺少相机朝向参数");
            }
            
            int deviceOrientation = getDeviceOrientation();
            
            // 获取平面数据
            Map<String, Object> yPlane = planes.get(0);
            Map<String, Object> uPlane = planes.get(1);
            Map<String, Object> vPlane = planes.get(2);
            
            byte[] yBytes = (byte[]) yPlane.get("bytes");
            byte[] uBytes = (byte[]) uPlane.get("bytes");
            byte[] vBytes = (byte[]) vPlane.get("bytes");
            
            Integer yBytesPerRow = (Integer) yPlane.get("bytesPerRow");
            Integer uBytesPerRow = (Integer) uPlane.get("bytesPerRow");
            Integer uBytesPerPixel = (Integer) uPlane.get("bytesPerPixel");
            
            // 处理YUV数据并转换为Bitmap
            Bitmap bitmap = processYUVImage(yBytes, uBytes, vBytes, width, height, 
                                          yBytesPerRow, uBytesPerRow, uBytesPerPixel);
            
            // 应用变换
            bitmap = transformBitmap(bitmap, sensorOrientation, deviceOrientation, isFront);
            
            // 创建MPImage并进行异步检测
            MPImage image = new BitmapImageBuilder(bitmap).build();
            poseLandmarker.detectAsync(image, System.currentTimeMillis());
            
            result.success(null);
            
        } catch (Exception e) {
            String errorMsg = "图像检测失败: " + e.getMessage();
            Log.e(TAG, errorMsg, e);
            result.error("DETECT_ERROR", errorMsg, null);
        }
    }

    private void detectRGBA(MethodCall call, Result result) throws Exception {
        try {
            if (poseLandmarker == null) {
                throw new Exception("请先调用 initialize 方法初始化姿势检测器");
            }
            
            Integer width = call.argument("width");
            Integer height = call.argument("height");
            byte[] imageData = call.argument("imageData");
            
            if (width == null) {
                throw new Exception("缺少图像宽度参数");
            }
            if (height == null) {
                throw new Exception("缺少图像高度参数");
            }
            if (imageData == null) {
                throw new Exception("缺少RGBA图像数据");
            }
            
            int expectedSize = width * height * 4;
            if (imageData.length != expectedSize) {
                throw new Exception("RGBA数据大小不符合预期: 实际 " + imageData.length + " 字节, 预期 " + expectedSize + " 字节");
            }
            
            Integer sensorOrientation = call.argument("sensorOrientation");
            if (sensorOrientation == null) {
                sensorOrientation = 0;
            }
            
            Boolean isFront = call.argument("isFront");
            if (isFront == null) {
                isFront = false;
            }
            
            int deviceOrientation = getDeviceOrientation();
            
            // 创建Bitmap
            Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            int[] pixels = new int[width * height];
            convertRGBAToARGB(imageData, pixels, width * height);
            bitmap.setPixels(pixels, 0, width, 0, 0, width, height);
            
            // 应用变换
            bitmap = transformBitmap(bitmap, sensorOrientation, deviceOrientation, isFront);
            
            // 创建MPImage并进行异步检测
            MPImage image = new BitmapImageBuilder(bitmap).build();
            poseLandmarker.detectAsync(image, System.currentTimeMillis());
            
            result.success(null);
            
        } catch (Exception e) {
            String errorMsg = "RGBA图像检测失败: " + e.getMessage();
            Log.e(TAG, errorMsg, e);
            result.error("DETECT_RGBA_ERROR", errorMsg, null);
        }
    }


    private void handleResult(PoseLandmarkerResult result) {
        try {
            List<List<Map<String, Object>>> poses = new ArrayList<>();
            
            // 处理每个人的姿势数据
            for (List<NormalizedLandmark> landmarks : result.landmarks()) {
                List<Map<String, Object>> posePoints = new ArrayList<>();
                
                for (NormalizedLandmark landmark : landmarks) {
                    Map<String, Object> point = new HashMap<>();
                    point.put("x", landmark.x());
                    point.put("y", landmark.y());
                    point.put("z", landmark.z());
                    point.put("visibility", landmark.visibility().orElse(0.0f));
                    point.put("presence", landmark.presence().orElse(0.0f));
                    posePoints.add(point);
                }
                
                poses.add(posePoints);
            }
            
            // 在主线程中发送结果
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    channel.invokeMethod("onResult", poses);
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "处理检测结果失败", e);
        }
    }

    // 原始代码中的YUV转RGB处理方法
    private void convertYUVToRGB(byte[] input, int width, int height, int[] output) throws Exception {
        int totalPixels = width * height;
        for (int i = 0; i < totalPixels; i++) {
            byte y = input[i];
            try {
                byte u = input[((i / 4) * 2) + totalPixels];
                byte v = input[((i / 4) * 2) + totalPixels + 1];
                
                float yf = y & 255;
                float uf = (u & 255) - 128;
                float vf = (v & 255) - 128;
                
                int r = clamp((int) (yf + (vf * 1.772f)), 0, 255);
                int g = clamp((int) ((1.402f * uf) + yf), 0, 255);  
                int b = clamp((int) ((yf - (0.344f * vf)) - (uf * 0.714f)), 0, 255);
                
                output[i] = 0xFF000000 | (r << 16) | (g << 8) | b;
            } catch (Exception e) {
                Log.e(TAG, "YUV420转RGB失败", e);
                throw e;
            }
        }
    }
    
    // 辅助方法：限制数值范围
    private int clamp(int value, int min, int max) {
        return Math.max(min, Math.min(max, value));
    }
    
    // 原始代码中的RGBA到ARGB转换
    private void convertRGBAToARGB(byte[] input, int[] output, int pixelCount) {
        for (int i = 0; i < pixelCount; i++) {
            int index = i * 4;
            output[i] = ((input[index + 3] & 255) << 24) |  // A
                       ((input[index] & 255) << 16) |      // R  
                       ((input[index + 1] & 255) << 8) |   // G
                       (input[index + 2] & 255);           // B
        }
    }
    
    // 处理YUV图像数据
    private Bitmap processYUVImage(byte[] yBytes, byte[] uBytes, byte[] vBytes, 
                                  int width, int height, int yBytesPerRow, 
                                  int uBytesPerRow, int uBytesPerPixel) throws Exception {
        try {
            int totalPixels = width * height;
            byte[] yuvData = new byte[(totalPixels * 3) / 2];
            
            // 复制Y平面数据
            int dstIndex = 0;
            for (int row = 0; row < height; row++) {
                System.arraycopy(yBytes, row * yBytesPerRow, yuvData, dstIndex, width);
                dstIndex += width;
            }
            
            // 交错复制U和V平面数据
            int uvHeight = height / 2;
            int uvWidth = width / 2;
            int uvDstIndex = totalPixels;
            int uSrcIndex = 0;
            int vSrcIndex = 0;
            
            for (int row = 0; row < uvHeight; row++) {
                for (int col = 0; col < uvWidth; col++) {
                    yuvData[uvDstIndex++] = vBytes[vSrcIndex];
                    yuvData[uvDstIndex++] = uBytes[uSrcIndex];
                    uSrcIndex += uBytesPerPixel;
                    vSrcIndex += uBytesPerPixel;
                }
                int skipBytes = uBytesPerRow - (uvWidth * uBytesPerPixel);
                vSrcIndex += skipBytes;
                uSrcIndex += skipBytes;
            }
            
            // 创建Bitmap并转换
            Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            int[] pixels = new int[totalPixels];
            convertYUVToRGB(yuvData, width, height, pixels);
            bitmap.setPixels(pixels, 0, width, 0, 0, width, height);
            
            return bitmap;
        } catch (Exception e) {
            Log.e(TAG, "处理YUV图像失败", e);
            throw e;
        }
    }
    
    // 获取设备方向
    private int getDeviceOrientation() {
        if (activity == null) {
            throw new IllegalStateException("Activity not available");
        }
        
        Display display = activity.getDisplay();
        int rotation = display != null ? display.getRotation() : 0;
        
        switch (rotation) {
            case 0: return 0;
            case 1: return 90;
            case 2: return 180;
            case 3: return 270;
            default: return 0;
        }
    }
    
    // 原始代码中的图像变换方法（简化版）
    private Bitmap transformBitmap(Bitmap bitmap, int sensorOrientation, int deviceOrientation, boolean isFront) throws Exception {
        try {
            // 这里实现具体的图像旋转和镜像逻辑
            // 原始代码中有复杂的变换逻辑，这里提供基本实现
            Matrix matrix = new Matrix();
            
            // 根据前置/后置摄像头和方向应用变换
            if (isFront) {
                matrix.preScale(-1.0f, 1.0f); // 水平翻转
            }
            
            // 应用旋转
            int rotationAngle = (sensorOrientation + deviceOrientation) % 360;
            if (rotationAngle != 0) {
                matrix.postRotate(rotationAngle);
            }
            
            return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
        } catch (Exception e) {
            Log.e(TAG, "图像变换失败", e);
            throw e;
        }
    }
    
    // 复制资源文件到缓存
    private String copyAssetToCache(Context context, String assetName) throws Exception {
        try {
            File cacheFile = new File(context.getCacheDir(), assetName);
            if (cacheFile.exists()) {
                if (cacheFile.length() == context.getAssets().openFd(assetName).getLength()) {
                    return cacheFile.getAbsolutePath();
                }
            }
            
            InputStream inputStream = context.getAssets().open(assetName);
            FileOutputStream outputStream = new FileOutputStream(cacheFile);
            
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
            
            outputStream.close();
            inputStream.close();
            
            return cacheFile.getAbsolutePath();
        } catch (Exception e) {
            Log.e(TAG, "复制模型文件到缓存失败", e);
            throw e;
        }
    }
    
    // 保存图像到相册（原始代码功能）
    private void saveImageToGallery(Bitmap bitmap, int width, int height, Context context) {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastSaveTime < SAVE_INTERVAL) {
            return;
        }
        
        try {
            String fileName = "POSE_" + 
                new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date()) + 
                "_" + width + "_" + height + ".jpg";
                
            File poseDir = new File(context.getExternalFilesDir(Environment.DIRECTORY_PICTURES), "poses");
            if (!poseDir.exists()) {
                poseDir.mkdirs();
            }
            
            File imageFile = new File(poseDir, fileName);
            FileOutputStream outputStream = new FileOutputStream(imageFile);
            
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream);
            outputStream.flush();
            outputStream.close();
            
            Log.i(TAG, "图片已保存: " + imageFile.getAbsolutePath());
            lastSaveTime = currentTime;
            
        } catch (Exception e) {
            Log.e(TAG, "保存图片失败", e);
        }
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) throws MediaPipeException {
        Log.i(TAG, "插件开始释放资源");
        
        if (channel != null) {
            channel.setMethodCallHandler(null);
        }
        
        if (poseLandmarker != null) {
            poseLandmarker.close();
        }
        
        Log.i(TAG, "插件资源释放完成");
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
        activity = binding.getActivity();
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {
        activity = null;
    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {
        activity = binding.getActivity();
    }

    @Override
    public void onDetachedFromActivity() {
        activity = null;
    }
}