{"buildFiles": ["D:\\soft\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\ai-dance\\ai-dance-flutter\\legend_dance\\build\\.cxx\\Debug\\2c6g4416\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\ai-dance\\ai-dance-flutter\\legend_dance\\build\\.cxx\\Debug\\2c6g4416\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}