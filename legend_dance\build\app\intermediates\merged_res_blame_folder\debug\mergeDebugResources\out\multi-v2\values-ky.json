{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3438,3538,3640,3743,3850,3954,4058,8614", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "3533,3635,3738,3845,3949,4053,4164,8710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,139,221", "endColumns": "83,81,84", "endOffsets": "134,216,301"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6890,6974,7056", "endColumns": "83,81,84", "endOffsets": "6969,7051,7136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,484,676,768,858,936,1026,1123,1210,1276,1371,1467,1535,1601,1666,1736,1816,1894,1975,2047,2128,2200,2288,2370,2452,2519,2585,2638,2699,2747,2808,2881,2957,3017,3087,3145,3202,3268,3333,3399,3451,3510,3586,3662", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,89,77,89,96,86,65,94,95,67,65,64,69,79,77,80,71,80,71,87,81,81,66,65,52,60,47,60,72,75,59,69,57,56,65,64,65,51,58,75,75,54", "endOffsets": "280,479,671,763,853,931,1021,1118,1205,1271,1366,1462,1530,1596,1661,1731,1811,1889,1970,2042,2123,2195,2283,2365,2447,2514,2580,2633,2694,2742,2803,2876,2952,3012,3082,3140,3197,3263,3328,3394,3446,3505,3581,3657,3712"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,534,4240,4332,4422,4500,4590,4687,4774,4840,4935,5031,5099,5165,5230,5300,5380,5458,5539,5611,5692,5764,5852,5934,6016,6083,6837,7141,7202,7250,7311,7384,7460,7520,7590,7648,7705,7771,7836,7902,7954,8013,8089,8165", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,91,89,77,89,96,86,65,94,95,67,65,64,69,79,77,80,71,80,71,87,81,81,66,65,52,60,47,60,72,75,59,69,57,56,65,64,65,51,58,75,75,54", "endOffsets": "330,529,721,4327,4417,4495,4585,4682,4769,4835,4930,5026,5094,5160,5225,5295,5375,5453,5534,5606,5687,5759,5847,5929,6011,6078,6144,6885,7197,7245,7306,7379,7455,7515,7585,7643,7700,7766,7831,7897,7949,8008,8084,8160,8215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "726,837,946,1058,1143,1248,1365,1444,1522,1613,1706,1801,1895,1995,2088,2183,2278,2369,2460,2541,2647,2752,2850,2957,3060,3175,3336,8532", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "832,941,1053,1138,1243,1360,1439,1517,1608,1701,1796,1890,1990,2083,2178,2273,2364,2455,2536,2642,2747,2845,2952,3055,3170,3331,3433,8609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,268,347,488,657,738", "endColumns": "70,91,78,140,168,80,78", "endOffsets": "171,263,342,483,652,733,812"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4169,8220,8312,8391,8715,8884,8965", "endColumns": "70,91,78,140,168,80,78", "endOffsets": "4235,8307,8386,8527,8879,8960,9039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,262,333,420,491,578,662", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "121,186,257,328,415,486,573,657,738"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6149,6220,6285,6356,6427,6514,6585,6672,6756", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "6215,6280,6351,6422,6509,6580,6667,6751,6832"}}]}]}