{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3484,3582,3684,3783,3885,3989,4093,8781", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3577,3679,3778,3880,3984,4088,4206,8877"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,263,335,418,495,592,685", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "124,189,258,330,413,490,587,680,763"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6249,6323,6388,6457,6529,6612,6689,6786,6879", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "6318,6383,6452,6524,6607,6684,6781,6874,6957"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "742,853,968,1078,1160,1266,1396,1474,1550,1641,1734,1832,1927,2027,2120,2213,2308,2399,2490,2576,2686,2797,2900,3011,3119,3226,3385,8694", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "848,963,1073,1155,1261,1391,1469,1545,1636,1729,1827,1922,2022,2115,2208,2303,2394,2485,2571,2681,2792,2895,3006,3114,3221,3380,3479,8776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,350,492,661,747", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "170,267,345,487,656,742,822"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4211,8377,8474,8552,8882,9051,9137", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "4276,8469,8547,8689,9046,9132,9212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,234", "endColumns": "87,90,91", "endOffsets": "138,229,321"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "7015,7103,7194", "endColumns": "87,90,91", "endOffsets": "7098,7189,7281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,483,692,781,872,951,1049,1146,1225,1291,1397,1504,1569,1635,1699,1771,1855,1942,2030,2105,2193,2266,2346,2440,2530,2596,2660,2713,2773,2821,2882,2953,3024,3091,3169,3234,3293,3359,3424,3490,3542,3602,3676,3750", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,83,86,87,74,87,72,79,93,89,65,63,52,59,47,60,70,70,66,77,64,58,65,64,65,51,59,73,73,53", "endOffsets": "286,478,687,776,867,946,1044,1141,1220,1286,1392,1499,1564,1630,1694,1766,1850,1937,2025,2100,2188,2261,2341,2435,2525,2591,2655,2708,2768,2816,2877,2948,3019,3086,3164,3229,3288,3354,3419,3485,3537,3597,3671,3745,3799"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,341,533,4281,4370,4461,4540,4638,4735,4814,4880,4986,5093,5158,5224,5288,5360,5444,5531,5619,5694,5782,5855,5935,6029,6119,6185,6962,7286,7346,7394,7455,7526,7597,7664,7742,7807,7866,7932,7997,8063,8115,8175,8249,8323", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,83,86,87,74,87,72,79,93,89,65,63,52,59,47,60,70,70,66,77,64,58,65,64,65,51,59,73,73,53", "endOffsets": "336,528,737,4365,4456,4535,4633,4730,4809,4875,4981,5088,5153,5219,5283,5355,5439,5526,5614,5689,5777,5850,5930,6024,6114,6180,6244,7010,7341,7389,7450,7521,7592,7659,7737,7802,7861,7927,7992,8058,8110,8170,8244,8318,8372"}}]}]}