{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,194,266,336,413,484,575,660", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "122,189,261,331,408,479,570,655,734"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6077,6149,6216,6288,6358,6435,6506,6597,6682", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "6144,6211,6283,6353,6430,6501,6592,6677,6756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "725,839,945,1054,1140,1244,1364,1441,1516,1608,1702,1797,1891,1992,2086,2182,2276,2368,2460,2545,2653,2759,2861,2972,3073,3189,3354,8414", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "834,940,1049,1135,1239,1359,1436,1511,1603,1697,1792,1886,1987,2081,2177,2271,2363,2455,2540,2648,2754,2856,2967,3068,3184,3349,3447,8495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,139,225", "endColumns": "83,85,82", "endOffsets": "134,220,303"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6814,6898,6984", "endColumns": "83,85,82", "endOffsets": "6893,6979,7062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,266,344,483,652,734", "endColumns": "72,87,77,138,168,81,75", "endOffsets": "173,261,339,478,647,729,805"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4173,8109,8197,8275,8601,8770,8852", "endColumns": "72,87,77,138,168,81,75", "endOffsets": "4241,8192,8270,8409,8765,8847,8923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3452,3550,3652,3754,3858,3961,4059,8500", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "3545,3647,3749,3853,3956,4054,4168,8596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,282,488,675,763,854,939,1034,1129,1197,1259,1348,1437,1507,1572,1634,1702,1782,1864,1943,2017,2098,2168,2236,2308,2379,2443,2506,2559,2617,2665,2726,2786,2855,2915,2978,3038,3101,3166,3229,3295,3348,3405,3476,3547", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,79,81,78,73,80,69,67,71,70,63,62,52,57,47,60,59,68,59,62,59,62,64,62,65,52,56,70,70,53", "endOffsets": "277,483,670,758,849,934,1029,1124,1192,1254,1343,1432,1502,1567,1629,1697,1777,1859,1938,2012,2093,2163,2231,2303,2374,2438,2501,2554,2612,2660,2721,2781,2850,2910,2973,3033,3096,3161,3224,3290,3343,3400,3471,3542,3596"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,332,538,4246,4334,4425,4510,4605,4700,4768,4830,4919,5008,5078,5143,5205,5273,5353,5435,5514,5588,5669,5739,5807,5879,5950,6014,6761,7067,7125,7173,7234,7294,7363,7423,7486,7546,7609,7674,7737,7803,7856,7913,7984,8055", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,79,81,78,73,80,69,67,71,70,63,62,52,57,47,60,59,68,59,62,59,62,64,62,65,52,56,70,70,53", "endOffsets": "327,533,720,4329,4420,4505,4600,4695,4763,4825,4914,5003,5073,5138,5200,5268,5348,5430,5509,5583,5664,5734,5802,5874,5945,6009,6072,6809,7120,7168,7229,7289,7358,7418,7481,7541,7604,7669,7732,7798,7851,7908,7979,8050,8104"}}]}]}