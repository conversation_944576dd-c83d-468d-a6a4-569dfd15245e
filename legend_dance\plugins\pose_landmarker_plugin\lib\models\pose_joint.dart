// 导入dart:core，因为它被隐式使用（例如 _Enum, Map, String, double, Object）
import 'dart:core';

// class id: 1026, size: 0x34, field offset: 0x8
/// 表示一个姿态连接点的数据模型。
class PoseJoint extends Object {
  /// X坐标
  final double x;

  /// Y坐标
  final double y;

  /// Z坐标
  final double z;

  /// 可见性，范围通常为[0.0, 1.0]。
  final double visibility;

  /// 存在性，范围通常为[0.0, 1.0]。
  final double presence;

  /// 其他元数据。
  final Map<String, dynamic>? metadata;

  /// 默认构造函数
  PoseJoint({
    required this.x,
    required this.y,
    required this.z,
    required this.visibility,
    required this.presence,
    this.metadata,
  });


  /// 从一个Map对象创建PoseJoint实例。
  /// 原始签名是 `factory PoseJoint PoseJoint.fromMap(dynamic, Map<String, dynamic>)`
  /// 第一个`dynamic`参数在汇编中未被使用，因此在Dart代码中省略。
  factory PoseJoint.fromMap(Map<String, dynamic> map) {
    // 从map中提取坐标和属性值
    // 对于可空数值，如果map中缺少或值为null，则提供默认值。
    final num? xValue = map['x'];
    final num? yValue = map['y'];
    final num? zValue = map['z'];
    final num? visibilityValue = map['visibility'];
    final num? presenceValue = map['presence'];

    return PoseJoint(
      x: (xValue ?? 0).toDouble(),
      y: (yValue ?? 0).toDouble(),
      z: (zValue ?? 0.0).toDouble(),
      visibility: (visibilityValue ?? 1.0).toDouble(),
      presence: (presenceValue ?? 1.0).toDouble(),
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 将PoseJoint实例转换为一个Map对象。
  Map<String, dynamic> toMap() {
    return {
      'x': x,
      'y': y,
      'z': z,
      'visibility': visibility,
      'presence': presence,
      'metadata': metadata,
    };
  }

  /// 返回表示此PoseJoint对象的字符串。
  @override
  String toString() {
    return 'PoseJoint(x: $x, y: $y, z: $z, visibility: $visibility, presence: $presence, metadata: $metadata)';
  }
}

// class id: 6126, size: 0x1c, field offset: 0x14
/// 表示姿态中不同关节的索引枚举。
/// 注意: 这里的枚举成员是从常见的姿态估计模型中推测的，
/// 因为原始汇编代码没有提供具体的枚举值定义。
enum JointIndex {
  nose,
  leftEyeInner,
  leftEye,
  leftEyeOuter,
  rightEyeInner,
  rightEye,
  rightEyeOuter,
  leftEar,
  rightEar,
  mouthLeft,
  mouthRight,
  leftShoulder,
  rightShoulder,
  leftElbow,
  rightElbow,
  leftWrist,
  rightWrist,
  leftPinky,
  rightPinky,
  leftIndex,
  rightIndex,
  leftThumb,
  rightThumb,
  leftHip,
  rightHip,
  leftKnee,
  rightKnee,
  leftAnkle,
  rightAnkle,
  leftHeel,
  rightHeel,
  leftFootIndex,
  rightFootIndex;

  // 原始汇编中包含一个`_enumToString`方法，它的功能等同于Dart SDK中
  // `Enum`基类提供的默认`toString`实现。
  // 在高版本的Dart中，`_Enum`已被`Enum`取代，并且`toString`方法是自动提供的。
  // 汇编代码逻辑为：'JointIndex.' + this.name
  // 现代Dart的 `toString()` 已经实现了这个功能，因此无需重写。
  /*
  @override
  String toString() {
    // 汇编分析显示此方法构建了 "JointIndex.<enum_name>" 格式的字符串。
    // 这与Dart中Enum的默认toString行为完全一致。
    return 'JointIndex.$name';
  }
  */
}
