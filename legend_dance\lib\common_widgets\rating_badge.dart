// lib: , url: package:keepdance/common_widgets/rating_badge.dart

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pose/utils/pose_rating_system.dart';
import 'dart:ui' show Color, Radius, TileMode, Shadow, BlurStyle, BoxShape;

// class id: 1049696, size: 0x8
class UnnamedClass {} // This class appears to be empty or unused in the provided context.

// class id: 4570, size: 0x34
class RatingBadge extends StatelessWidget {
  final dynamic score;
  final double margin;
  final double width;
  final double height;
  final double radius;
  final bool isRepaintBoundary;

  const RatingBadge({
    super.key,
    required this.score,
    this.margin = 0.0,
    this.width = 0.0,
    this.height = 0.0,
    this.radius = 0.0,
    this.isRepaintBoundary = false,
  });

  factory RatingBadge.forMaterialCard(dynamic score) {
    return RatingBadge(
      score: score,
      margin: 8.0,
      width: 24.0,
      height: 12.0,
      radius: 8.0,
      isRepaintBoundary: true,
    );
  }

  double _parseScore(dynamic scoreValue) {
    if (scoreValue == null) {
      return 0.0;
    }
    if (scoreValue is double) {
      return scoreValue;
    }
    if (scoreValue is int) {
      return scoreValue.toDouble();
    }
    if (scoreValue is String) {
      return double.tryParse(scoreValue) ?? 0.0;
    }
    // Fallback for other types by converting to string first
    return double.tryParse(scoreValue.toString()) ?? 0.0;
  }

  @override
  Widget build(BuildContext context) {
    final double parsedScore = _parseScore(score);

    // If score is less than 30, return an empty box.
    if (30.0 > parsedScore) {
      return const SizedBox();
    }
    
    final String rank = PoseRatingSystem.getRank(parsedScore);

    double iconSize;
    // Calculation based on rank length. Corresponds to: 24.0 + (rank.length - 1) * 12.0 * 0.6
    if (rank.length > 1) {
        iconSize = 24.0 + (rank.length - 1) * 7.2;
    } else {
        iconSize = 24.0;
    }

    Widget badgeContent = Container(
      decoration: BoxDecoration(
        color: const Color(0xfff3f3f3).withOpacity(0.25),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: const [0.0, 0.6],
          colors: [
            const Color(0xfff3f3f3).withOpacity(0.25),
            const Color(0xffffffff),
          ],
          tileMode: TileMode.clamp,
        ),
        borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
        shape: BoxShape.rectangle,
      ),
      child: Center(
        // alignment: Alignment.center, // 移除不支持的参数
        child: Text(
          rank,
          style: TextStyle(
            inherit: true,
            color: const Color(0xfff3f3f3),
            fontSize: 12.0.sp,
            fontWeight: FontWeight.w700,
            height: 1.0,
            letterSpacing: 0.25,
            shadows: [
              Shadow(
                color: const Color(0xff000000).withOpacity(0.25),
                offset: const Offset(0.0, 2.0),
                blurRadius: 2.0,
              ),
            ],
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );

    Widget badgeContainer = Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: const Color(0xfff3f3f3).withOpacity(0.6),
          width: 1.5,
        ),
        borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
        boxShadow: [
          BoxShadow(
            color: const Color(0xff000000).withOpacity(0.12),
            offset: const Offset(0, 6),
            blurRadius: 6.0,
            spreadRadius: 0.0,
            blurStyle: BlurStyle.normal,
          ),
          BoxShadow(
            color: const Color(0xff000000).withOpacity(0.08),
            offset: const Offset(0, 12),
            blurRadius: 12.0,
            spreadRadius: 0.0,
            blurStyle: BlurStyle.normal,
          ),
        ],
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: const [0.0, 0.5, 1.0],
          colors: [
            const Color(0xff8c8c8c).withOpacity(0.9),
            const Color(0xffb1b1b1).withOpacity(0.85),
            const Color(0xffdcdcdc).withOpacity(0.8),
          ],
          tileMode: TileMode.clamp,
        ),
        shape: BoxShape.rectangle,
      ),
      child: Material(
          type: MaterialType.canvas,
          elevation: 0.0,
          color: const Color(0xffffffff),
          shadowColor: null,
          textStyle: null,
          borderRadius: null,
          child: badgeContent,
          animationDuration: const Duration(milliseconds: 200),
          clipBehavior: Clip.none,
          borderOnForeground: true),
    );

    Widget finalWidget = Container(
      width: iconSize.w,
      height: 24.0.h,
      margin: EdgeInsets.all(8.0.w),
      decoration: null,
      foregroundDecoration: null,
      alignment: null,
      padding: null,
      constraints: null,
      child: badgeContainer,
    );
    
    // The assembly suggests isRepaintBoundary is an option, but the call from forMaterialCard sets it to true.
    // However, the build method logic doesn't use the 'isRepaintBoundary' field to conditionally wrap the widget.
    // It unconditionally wraps the final widget in a RepaintBoundary.
    return RepaintBoundary(
      child: finalWidget,
    );
  }
}

