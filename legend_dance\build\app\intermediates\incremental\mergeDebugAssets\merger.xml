<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":video_thumbnail" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\video_thumbnail\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":video_player_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\video_player_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":sqflite_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\sqflite_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\permission_handler_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\path_provider_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\flutter_plugin_android_lifecycle\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":connectivity_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\connectivity_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":pose" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\pose\intermediates\library_assets\debug\packageDebugAssets\out"><file name="pose_landmarker_lite.task" path="E:\ai-dance\ai-dance-flutter\legend_dance\build\pose\intermediates\library_assets\debug\packageDebugAssets\out\pose_landmarker_lite.task"/></source></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\shared_preferences_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":share_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\share_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":package_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\package_info_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":wakelock_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\wakelock_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":better_player" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\better_player\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":fluwx" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":device_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\device_info_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":camera_android_camerax" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\camera_android_camerax\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\ai-dance\ai-dance-flutter\legend_dance\build\app\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>