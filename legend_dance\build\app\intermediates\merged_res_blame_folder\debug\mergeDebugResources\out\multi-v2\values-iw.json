{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,311,385,447,527,607", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "115,174,241,306,380,442,522,602,664"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6128,6193,6252,6319,6384,6458,6520,6600,6680", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "6188,6247,6314,6379,6453,6515,6595,6675,6737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1030,1130,1238,1322,1424,1540,1619,1697,1788,1882,1976,2070,2170,2263,2358,2451,2542,2634,2715,2820,2923,3021,3126,3228,3330,3484,8375", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "1025,1125,1233,1317,1419,1535,1614,1692,1783,1877,1971,2065,2165,2258,2353,2446,2537,2629,2710,2815,2918,3016,3121,3223,3325,3479,3576,8452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,582,875,954,1032,1108,1193,1277,1339,1401,1490,1576,1641,1705,1768,1836,1913,1997,2078,2149,2226,2295,2356,2443,2529,2593,2656,2710,2781,2829,2890,2949,3016,3077,3140,3201,3258,3324,3388,3454,3506,3560,3628,3696", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,76,83,80,70,76,68,60,86,85,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "279,577,870,949,1027,1103,1188,1272,1334,1396,1485,1571,1636,1700,1763,1831,1908,1992,2073,2144,2221,2290,2351,2438,2524,2588,2651,2705,2776,2824,2885,2944,3011,3072,3135,3196,3253,3319,3383,3449,3501,3555,3623,3691,3745"}, "to": {"startLines": "2,11,17,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,334,632,4347,4426,4504,4580,4665,4749,4811,4873,4962,5048,5113,5177,5240,5308,5385,5469,5550,5621,5698,5767,5828,5915,6001,6065,6742,7050,7121,7169,7230,7289,7356,7417,7480,7541,7598,7664,7728,7794,7846,7900,7968,8036", "endLines": "10,16,22,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,76,83,80,70,76,68,60,86,85,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "329,627,920,4421,4499,4575,4660,4744,4806,4868,4957,5043,5108,5172,5235,5303,5380,5464,5545,5616,5693,5762,5823,5910,5996,6060,6123,6791,7116,7164,7225,7284,7351,7412,7475,7536,7593,7659,7723,7789,7841,7895,7963,8031,8085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "50,51,52,53,54,55,56,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3581,3675,3777,3874,3971,4072,4172,8457", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3670,3772,3869,3966,4067,4167,4273,8553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,258,334,459,628,709", "endColumns": "68,83,75,124,168,80,78", "endOffsets": "169,253,329,454,623,704,783"}, "to": {"startLines": "57,112,113,114,117,118,119", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4278,8090,8174,8250,8558,8727,8808", "endColumns": "68,83,75,124,168,80,78", "endOffsets": "4342,8169,8245,8370,8722,8803,8882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,224", "endColumns": "80,87,84", "endOffsets": "131,219,304"}, "to": {"startLines": "92,93,94", "startColumns": "4,4,4", "startOffsets": "6796,6877,6965", "endColumns": "80,87,84", "endOffsets": "6872,6960,7045"}}]}]}