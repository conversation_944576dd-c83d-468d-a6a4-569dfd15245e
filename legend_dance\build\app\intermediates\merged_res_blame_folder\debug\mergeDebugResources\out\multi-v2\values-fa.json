{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,216", "endColumns": "77,82,79", "endOffsets": "128,211,291"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6746,6824,6907", "endColumns": "77,82,79", "endOffsets": "6819,6902,6982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,258,335,467,636,718", "endColumns": "66,85,76,131,168,81,77", "endOffsets": "167,253,330,462,631,713,791"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4160,8057,8143,8220,8535,8704,8786", "endColumns": "66,85,76,131,168,81,77", "endOffsets": "4222,8138,8215,8347,8699,8781,8859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "746,856,957,1068,1152,1253,1368,1448,1525,1618,1713,1805,1899,2001,2096,2193,2287,2380,2470,2552,2660,2764,2862,2968,3073,3178,3335,8352", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "851,952,1063,1147,1248,1363,1443,1520,1613,1708,1800,1894,1996,2091,2188,2282,2375,2465,2547,2655,2759,2857,2963,3068,3173,3330,3431,8429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,183,250,310,388,463,552,640", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "117,178,245,305,383,458,547,635,700"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6042,6109,6170,6237,6297,6375,6450,6539,6627", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "6104,6165,6232,6292,6370,6445,6534,6622,6687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3436,3535,3637,3736,3836,3937,4043,8434", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3530,3632,3731,3831,3932,4038,4155,8530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,313,512,696,779,864,943,1036,1128,1205,1268,1360,1447,1510,1572,1633,1700,1774,1853,1929,1998,2074,2144,2216,2296,2374,2437,2511,2565,2634,2682,2743,2801,2878,2942,3006,3066,3128,3193,3259,3325,3377,3436,3509,3582", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,73,78,75,68,75,69,71,79,77,62,73,53,68,47,60,57,76,63,63,59,61,64,65,65,51,58,72,72,52", "endOffsets": "308,507,691,774,859,938,1031,1123,1200,1263,1355,1442,1505,1567,1628,1695,1769,1848,1924,1993,2069,2139,2211,2291,2369,2432,2506,2560,2629,2677,2738,2796,2873,2937,3001,3061,3123,3188,3254,3320,3372,3431,3504,3577,3630"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,363,562,4227,4310,4395,4474,4567,4659,4736,4799,4891,4978,5041,5103,5164,5231,5305,5384,5460,5529,5605,5675,5747,5827,5905,5968,6692,6987,7056,7104,7165,7223,7300,7364,7428,7488,7550,7615,7681,7747,7799,7858,7931,8004", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,73,78,75,68,75,69,71,79,77,62,73,53,68,47,60,57,76,63,63,59,61,64,65,65,51,58,72,72,52", "endOffsets": "358,557,741,4305,4390,4469,4562,4654,4731,4794,4886,4973,5036,5098,5159,5226,5300,5379,5455,5524,5600,5670,5742,5822,5900,5963,6037,6741,7051,7099,7160,7218,7295,7359,7423,7483,7545,7610,7676,7742,7794,7853,7926,7999,8052"}}]}]}