// lib: , url: package:keepdance/pose/utils/pose_rating_system.dart

/// 一个工具类，用于根据分数计算姿态评级，如星级和等级。
///
/// 此类包含静态方法，可将一个数值分数（推测范围为 0.0 到 1.0）映射到特定的评级。
/// 映射关系由内部的常量Map定义。
abstract class PoseRatingSystem {

  /// 根据给定的分数确定星级。
  ///
  /// 该评级是通过将分数与一系列降序排列的阈值进行比较来确定的。
  /// 它返回分数所超过的第一个阈值对应的星级。
  ///
  /// 例如，假设阈值为 {5: 0.9, 4: 0.8}：
  /// - 一个 0.85 的分数将返回 4，因为它小于等于 0.9 但大于 0.8。
  /// - 一个 0.9 的分数也将返回 4，因为汇编中的比较是严格大于 (>)，`0.9 > 0.9` 为 false。
  ///
  /// [score]: 数值分数，为一个 [double]。
  ///
  /// 返回一个代表星级数量的 [int]。如果分数低于所有阈值，则返回 0。
  static int getStars(double score) {
    // 注意: 此Map是根据反编译的汇编推断的结构。
    // 原始Map是一个包含6个条目的`_ConstMap` (`Map<int, double>`)，其确切值无法恢复。
    // 汇编逻辑要求条目按其值（分数阈值）降序排列。
    const Map<int, double> starThresholds = {
      // 这里的键值对是占位符，仅用于演示。
      // 实际值嵌入在原始二进制文件中。
      // 5: 0.9,
      // 4: 0.8,
      // 3: 0.7,
      // 2: 0.6,
      // 1: 0.5,
      // ...
    };

    // 汇编代码的逻辑等同于查找第一个 `score > entry.value` 的条目。
    final entry = starThresholds.entries.firstWhere(
      (entry) => score > entry.value,
      orElse: () => const MapEntry(0, 0.0), // 如果没有找到，返回默认值。
    );

    // 如果找到了条目，则返回其键（星级）。
    // 否则，返回默认值 0，这与汇编代码在循环结束后返回0的行为一致。
    return entry?.key ?? 0;
  }

  /// 根据给定的分数确定评级称号。
  ///
  /// 其逻辑与 [getStars] 完全相同，但它将分数映射到一个 [String] 类型的评级
  /// (例如, "S", "A", "B")。
  ///
  /// [score]: 数值分数，为一个 [double]。
  ///
  /// 返回一个代表评级的 [String]。如果分数低于所有阈值，则返回 "暂无评级"。
  static String getRank(double score) {
    // 注意: 此Map是根据反编译的汇编推断的结构。
    // 原始Map是一个包含6个条目的`_ConstMap` (`Map<String, double>`)，其确切值无法恢复。
    // 汇编逻辑要求条目按其值（分数阈值）降序排列。
    const Map<String, double> rankThresholds = {
      // 这里的键值对是占位符，仅用于演示。
      // 实际值嵌入在原始二进制文件中。
      // 'S': 0.9,
      // 'A': 0.8,
      // 'B': 0.7,
      // 'C': 0.6,
      // 'D': 0.5,
      // ...
    };

    // 汇编代码的逻辑等同于查找第一个 `score > entry.value` 的条目。
    final entry = rankThresholds.entries.firstWhere(
      (entry) => score > entry.value,
      orElse: () => const MapEntry("暂无评级", 0.0), // 如果没有找到，返回默认值。
    );

    // 如果找到了条目，则返回其键（评级名称）。
    // 否则，返回默认值 "暂无评级"，这与汇编代码的行为一致。
    return entry?.key ?? "暂无评级";
  }
}
