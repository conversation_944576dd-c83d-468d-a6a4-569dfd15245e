{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,487,666,752,840,923,1022,1120,1201,1267,1380,1490,1563,1632,1698,1769,1846,1931,2008,2077,2165,2240,2322,2419,2512,2576,2640,2693,2751,2799,2860,2925,2994,3059,3131,3195,3252,3318,3382,3448,3501,3561,3635,3709", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,82,98,97,80,65,112,109,72,68,65,70,76,84,76,68,87,74,81,96,92,63,63,52,57,47,60,64,68,64,71,63,56,65,63,65,52,59,73,73,56", "endOffsets": "280,482,661,747,835,918,1017,1115,1196,1262,1375,1485,1558,1627,1693,1764,1841,1926,2003,2072,2160,2235,2317,2414,2507,2571,2635,2688,2746,2794,2855,2920,2989,3054,3126,3190,3247,3313,3377,3443,3496,3556,3630,3704,3761"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,537,4241,4327,4415,4498,4597,4695,4776,4842,4955,5065,5138,5207,5273,5344,5421,5506,5583,5652,5740,5815,5897,5994,6087,6151,6918,7222,7280,7328,7389,7454,7523,7588,7660,7724,7781,7847,7911,7977,8030,8090,8164,8238", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,85,87,82,98,97,80,65,112,109,72,68,65,70,76,84,76,68,87,74,81,96,92,63,63,52,57,47,60,64,68,64,71,63,56,65,63,65,52,59,73,73,56", "endOffsets": "330,532,711,4322,4410,4493,4592,4690,4771,4837,4950,5060,5133,5202,5268,5339,5416,5501,5578,5647,5735,5810,5892,5989,6082,6146,6210,6966,7275,7323,7384,7449,7518,7583,7655,7719,7776,7842,7906,7972,8025,8085,8159,8233,8290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3441,3537,3639,3738,3835,3941,4046,8693", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "3532,3634,3733,3830,3936,4041,4167,8789"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,270,347,490,659,746", "endColumns": "68,95,76,142,168,86,80", "endOffsets": "169,265,342,485,654,741,822"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4172,8295,8391,8468,8794,8963,9050", "endColumns": "68,95,76,142,168,86,80", "endOffsets": "4236,8386,8463,8606,8958,9045,9126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "716,839,944,1051,1134,1240,1366,1450,1529,1620,1713,1806,1901,1999,2092,2185,2279,2370,2461,2542,2653,2761,2859,2969,3074,3182,3342,8611", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "834,939,1046,1129,1235,1361,1445,1524,1615,1708,1801,1896,1994,2087,2180,2274,2365,2456,2537,2648,2756,2854,2964,3069,3177,3337,3436,8688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,225", "endColumns": "80,88,80", "endOffsets": "131,220,301"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6971,7052,7141", "endColumns": "80,88,80", "endOffsets": "7047,7136,7217"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,261,331,407,483,581,676", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "128,187,256,326,402,478,576,671,753"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6215,6293,6352,6421,6491,6567,6643,6741,6836", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "6288,6347,6416,6486,6562,6638,6736,6831,6913"}}]}]}