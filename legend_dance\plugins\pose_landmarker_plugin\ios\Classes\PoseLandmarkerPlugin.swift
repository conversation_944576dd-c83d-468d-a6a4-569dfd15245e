import Flutter
import UIKit
import MediaPipeTasksVision
import AVFoundation

public class PoseLandmarkerPlugin: NSObject, FlutterPlugin {
    private static let channelName = "pose_landmarker_plugin"
    private static let tag = "PoseLandmarker"
    private static let saveInterval: TimeInterval = 10.0 // 保存间隔
    
    private var channel: FlutterMethodChannel?
    private var poseLandmarker: PoseLandmarker?
    private var lastSaveTime: TimeInterval = 0
    
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: channelName, binaryMessenger: registrar.messenger())
        let instance = PoseLandmarkerPlugin()
        instance.channel = channel
        registrar.addMethodCallDelegate(instance, channel: channel)
        print("\(tag): 插件开始初始化")
    }
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "initialize":
            initialize(call: call, result: result)
        case "detect":
            detect(call: call, result: result)
        case "detectRGBA":
            detectRGBA(call: call, result: result)
        default:
            print("\(PoseLandmarkerPlugin.tag): 未实现的方法: \(call.method)")
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func initialize(call: FlutterMethodCall, result: @escaping FlutterResult) {
        print("\(PoseLandmarkerPlugin.tag): 开始初始化姿势检测器")
        
        guard let args = call.arguments as? [String: Any] else {
            let errorMsg = "姿势检测器初始化失败: 无效参数"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "INITIALIZE_ERROR", message: errorMsg, details: nil))
            return
        }
        
        let maxPoses = args["maxPoses"] as? Int ?? 1
        
        // 释放现有的检测器
        poseLandmarker = nil
        
        do {
            // 复制模型文件到缓存
            guard let modelPath = copyAssetToCache(assetName: "pose_landmarker_lite.task") else {
                let errorMsg = "姿势检测器初始化失败: 复制模型文件到缓存失败"
                print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
                result(FlutterError(code: "INITIALIZE_ERROR", message: errorMsg, details: nil))
                return
            }
            
            // 创建基础选项
            let baseOptions = BaseOptions()
            baseOptions.modelAssetPath = modelPath
            baseOptions.delegate = .GPU
            
            // 创建PoseLandmarker选项
            let options = PoseLandmarkerOptions()
            options.baseOptions = baseOptions
            options.runningMode = .liveStream
            options.numPoses = maxPoses
            options.poseLandmarkerLiveStreamDelegate = self
            
            // 创建PoseLandmarker
            poseLandmarker = try PoseLandmarker(options: options)
            
            print("\(PoseLandmarkerPlugin.tag): 姿势检测器初始化成功,最大检测人数: \(maxPoses)")
            result(nil)
            
        } catch {
            let errorMsg = "姿势检测器初始化失败: \(error.localizedDescription)"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "INITIALIZE_ERROR", message: errorMsg, details: nil))
        }
    }
    
    private func detect(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let poseLandmarker = poseLandmarker else {
            let errorMsg = "图像检测失败: 请先调用 initialize 方法初始化姿势检测器"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_ERROR", message: errorMsg, details: nil))
            return
        }
        
        guard let args = call.arguments as? [String: Any] else {
            let errorMsg = "图像检测失败: 无效参数"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_ERROR", message: errorMsg, details: nil))
            return
        }
        
        guard let width = args["width"] as? Int else {
            let errorMsg = "图像检测失败: 缺少图像宽度参数"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_ERROR", message: errorMsg, details: nil))
            return
        }
        
        guard let height = args["height"] as? Int else {
            let errorMsg = "图像检测失败: 缺少图像高度参数"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_ERROR", message: errorMsg, details: nil))
            return
        }
        
        guard let planes = args["planes"] as? [[String: Any]] else {
            let errorMsg = "图像检测失败: 缺少图像平面数据"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_ERROR", message: errorMsg, details: nil))
            return
        }
        
        if planes.count != 3 {
            let errorMsg = "图像检测失败: 图像平面数据格式错误,需要 3 个平面(Y/U/V),实际收到 \(planes.count) 个平面"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_ERROR", message: errorMsg, details: nil))
            return
        }
        
        guard let sensorOrientation = args["sensorOrientation"] as? Int else {
            let errorMsg = "图像检测失败: 缺少传感器方向参数"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_ERROR", message: errorMsg, details: nil))
            return
        }
        
        guard let isFront = args["isFront"] as? Bool else {
            let errorMsg = "图像检测失败: 缺少相机朝向参数"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_ERROR", message: errorMsg, details: nil))
            return
        }
        
        let deviceOrientation = getDeviceOrientation()
        
        do {
            // 处理YUV图像数据并转换为UIImage
            let image = try processYUVImage(planes: planes, width: width, height: height)
            
            // 应用变换
            let transformedImage = try transformImage(image, sensorOrientation: sensorOrientation, 
                                                    deviceOrientation: deviceOrientation, isFront: isFront)
            
            // 创建MPImage并进行异步检测
            let mpImage = try MPImage(uiImage: transformedImage)
            let timestamp = Int(Date().timeIntervalSince1970 * 1000)
            
            try poseLandmarker.detectAsync(image: mpImage, timestampInMilliseconds: timestamp)
            
            result(nil)
            
        } catch {
            let errorMsg = "图像检测失败: \(error.localizedDescription)"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_ERROR", message: errorMsg, details: nil))
        }
    }
    
    private func detectRGBA(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let poseLandmarker = poseLandmarker else {
            let errorMsg = "RGBA图像检测失败: 请先调用 initialize 方法初始化姿势检测器"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_RGBA_ERROR", message: errorMsg, details: nil))
            return
        }
        
        guard let args = call.arguments as? [String: Any] else {
            let errorMsg = "RGBA图像检测失败: 无效参数"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_RGBA_ERROR", message: errorMsg, details: nil))
            return
        }
        
        guard let width = args["width"] as? Int else {
            let errorMsg = "RGBA图像检测失败: 缺少图像宽度参数"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_RGBA_ERROR", message: errorMsg, details: nil))
            return
        }
        
        guard let height = args["height"] as? Int else {
            let errorMsg = "RGBA图像检测失败: 缺少图像高度参数"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_RGBA_ERROR", message: errorMsg, details: nil))
            return
        }
        
        guard let imageData = args["imageData"] as? FlutterStandardTypedData else {
            let errorMsg = "RGBA图像检测失败: 缺少RGBA图像数据"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_RGBA_ERROR", message: errorMsg, details: nil))
            return
        }
        
        let expectedSize = width * height * 4
        if imageData.data.count != expectedSize {
            let errorMsg = "RGBA图像检测失败: RGBA数据大小不符合预期: 实际 \(imageData.data.count) 字节, 预期 \(expectedSize) 字节"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_RGBA_ERROR", message: errorMsg, details: nil))
            return
        }
        
        let sensorOrientation = args["sensorOrientation"] as? Int ?? 0
        let isFront = args["isFront"] as? Bool ?? false
        let deviceOrientation = getDeviceOrientation()
        
        do {
            // 创建UIImage
            let image = try convertRGBAToUIImage(data: imageData.data, width: width, height: height)
            
            // 应用变换
            let transformedImage = try transformImage(image, sensorOrientation: sensorOrientation,
                                                    deviceOrientation: deviceOrientation, isFront: isFront)
            
            // 创建MPImage并进行异步检测
            let mpImage = try MPImage(uiImage: transformedImage)
            let timestamp = Int(Date().timeIntervalSince1970 * 1000)
            
            try poseLandmarker.detectAsync(image: mpImage, timestampInMilliseconds: timestamp)
            
            result(nil)
            
        } catch {
            let errorMsg = "RGBA图像检测失败: \(error.localizedDescription)"
            print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
            result(FlutterError(code: "DETECT_RGBA_ERROR", message: errorMsg, details: nil))
        }
    }
    
    // MARK: - 辅助方法 - 符合原始APK功能
    
    /// 原始代码中的YUV转RGB处理方法
    private func convertYUVToRGB(_ input: Data, width: Int, height: Int) throws -> [UInt8] {
        let totalPixels = width * height
        var output = [UInt32](repeating: 0, count: totalPixels)
        
        for i in 0..<totalPixels {
            let y = input[i]
            
            do {
                let u = input[((i / 4) * 2) + totalPixels]
                let v = input[((i / 4) * 2) + totalPixels + 1]
                
                let yf = Float(y)
                let uf = Float(u) - 128
                let vf = Float(v) - 128
                
                let r = clamp(Int(yf + (vf * 1.772)), min: 0, max: 255)
                let g = clamp(Int((1.402 * uf) + yf), min: 0, max: 255)
                let b = clamp(Int((yf - (0.344 * vf)) - (uf * 0.714)), min: 0, max: 255)
                
                output[i] = 0xFF000000 | (UInt32(r) << 16) | (UInt32(g) << 8) | UInt32(b)
            } catch {
                print("\(PoseLandmarkerPlugin.tag): YUV420转RGB失败")
                throw error
            }
        }
        
        return output.withUnsafeBufferPointer { buffer in
            return buffer.bindMemory(to: UInt8.self).map { $0 }
        }
    }
    
    /// 辅助方法：限制数值范围
    private func clamp<T: Comparable>(_ value: T, min: T, max: T) -> T {
        return Swift.max(min, Swift.min(max, value))
    }
    
    /// 原始代码中的RGBA到ARGB转换
    private func convertRGBAToARGB(_ input: Data, pixelCount: Int) -> [UInt32] {
        var output = [UInt32](repeating: 0, count: pixelCount)
        
        for i in 0..<pixelCount {
            let index = i * 4
            output[i] = (UInt32(input[index + 3]) << 24) |  // A
                       (UInt32(input[index]) << 16) |      // R  
                       (UInt32(input[index + 1]) << 8) |   // G
                       UInt32(input[index + 2])            // B
        }
        
        return output
    }
    
    /// 处理YUV图像数据
    private func processYUVImage(planes: [[String: Any]], width: Int, height: Int) throws -> UIImage {
        guard let yPlaneData = planes[0]["bytes"] as? FlutterStandardTypedData,
              let uPlaneData = planes[1]["bytes"] as? FlutterStandardTypedData,
              let vPlaneData = planes[2]["bytes"] as? FlutterStandardTypedData,
              let yBytesPerRow = planes[0]["bytesPerRow"] as? Int,
              let uBytesPerRow = planes[1]["bytesPerRow"] as? Int,
              let uBytesPerPixel = planes[1]["bytesPerPixel"] as? Int else {
            throw NSError(domain: "ProcessYUV", code: -1, userInfo: [NSLocalizedDescriptionKey: "处理YUV图像失败: 无效的平面数据"])
        }
        
        let yBytes = yPlaneData.data
        let uBytes = uPlaneData.data  
        let vBytes = vPlaneData.data
        
        let totalPixels = width * height
        var yuvData = Data(count: (totalPixels * 3) / 2)
        
        // 复制Y平面数据
        var dstIndex = 0
        for row in 0..<height {
            let srcStart = row * yBytesPerRow
            yuvData.replaceSubrange(dstIndex..<(dstIndex + width), with: yBytes[srcStart..<(srcStart + width)])
            dstIndex += width
        }
        
        // 交错复制U和V平面数据
        let uvHeight = height / 2
        let uvWidth = width / 2
        var uvDstIndex = totalPixels
        var uSrcIndex = 0
        var vSrcIndex = 0
        
        for _ in 0..<uvHeight {
            for _ in 0..<uvWidth {
                yuvData[uvDstIndex] = vBytes[vSrcIndex]
                yuvData[uvDstIndex + 1] = uBytes[uSrcIndex]
                uvDstIndex += 2
                uSrcIndex += uBytesPerPixel
                vSrcIndex += uBytesPerPixel
            }
            let skipBytes = uBytesPerRow - (uvWidth * uBytesPerPixel)
            vSrcIndex += skipBytes
            uSrcIndex += skipBytes
        }
        
        // 转换为RGB
        let rgbData = try convertYUVToRGB(yuvData, width: width, height: height)
        
        // 创建UIImage
        return try createUIImage(from: rgbData, width: width, height: height)
    }
    
    /// 获取设备方向
    private func getDeviceOrientation() -> Int {
        let orientation = UIApplication.shared.statusBarOrientation
        switch orientation {
        case .portrait: return 0
        case .landscapeLeft: return 90
        case .portraitUpsideDown: return 180  
        case .landscapeRight: return 270
        default: return 0
        }
    }
    
    /// 原始代码中的图像变换方法
    private func transformImage(_ image: UIImage, sensorOrientation: Int, deviceOrientation: Int, isFront: Bool) throws -> UIImage {
        guard let cgImage = image.cgImage else {
            throw NSError(domain: "TransformImage", code: -1, userInfo: [NSLocalizedDescriptionKey: "图像变换失败: 无效的CGImage"])
        }
        
        let rotationAngle = (sensorOrientation + deviceOrientation) % 360
        var transform = CGAffineTransform.identity
        
        // 根据前置/后置摄像头和方向应用变换
        if isFront {
            transform = transform.scaledBy(x: -1.0, y: 1.0) // 水平翻转
        }
        
        // 应用旋转
        if rotationAngle != 0 {
            let radians = CGFloat(rotationAngle) * CGFloat.pi / 180.0
            transform = transform.rotated(by: radians)
        }
        
        let renderer = UIGraphicsImageRenderer(size: image.size)
        let transformedImage = renderer.image { context in
            context.cgContext.concatenate(transform)
            context.cgContext.draw(cgImage, in: CGRect(origin: .zero, size: image.size))
        }
        
        return transformedImage
    }
    
    /// 复制资源文件到缓存
    private func copyAssetToCache(assetName: String) -> String? {
        do {
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let cacheFile = documentsPath.appendingPathComponent(assetName)
            
            if FileManager.default.fileExists(atPath: cacheFile.path) {
                guard let bundlePath = Bundle.main.path(forResource: String(assetName.dropLast(5)), ofType: "task") else {
                    return cacheFile.path
                }
                
                let bundleAttributes = try FileManager.default.attributesOfItem(atPath: bundlePath)
                let cacheAttributes = try FileManager.default.attributesOfItem(atPath: cacheFile.path)
                
                if let bundleSize = bundleAttributes[.size] as? NSNumber,
                   let cacheSize = cacheAttributes[.size] as? NSNumber,
                   bundleSize == cacheSize {
                    return cacheFile.path
                }
            }
            
            guard let bundlePath = Bundle.main.path(forResource: String(assetName.dropLast(5)), ofType: "task") else {
                print("\(PoseLandmarkerPlugin.tag): 复制模型文件到缓存失败: 模型文件未找到")
                return nil
            }
            
            try FileManager.default.copyItem(atPath: bundlePath, toPath: cacheFile.path)
            
            return cacheFile.path
        } catch {
            print("\(PoseLandmarkerPlugin.tag): 复制模型文件到缓存失败: \(error)")
            return nil
        }
    }
    
    /// 从RGBA数据创建UIImage
    private func convertRGBAToUIImage(data: Data, width: Int, height: Int) throws -> UIImage {
        let totalPixels = width * height
        let rgbaPixels = convertRGBAToARGB(data, pixelCount: totalPixels)
        
        let rgbData = rgbaPixels.withUnsafeBufferPointer { buffer in
            return Data(buffer: buffer.bindMemory(to: UInt8.self))
        }
        
        return try createUIImage(from: rgbData, width: width, height: height)
    }
    
    /// 从RGB数据创建UIImage
    private func createUIImage(from data: Data, width: Int, height: Int) throws -> UIImage {
        let bytesPerPixel = 4
        let bytesPerRow = width * bytesPerPixel
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        
        guard let context = CGContext(
            data: nil,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else {
            throw NSError(domain: "CreateUIImage", code: -1, userInfo: [NSLocalizedDescriptionKey: "创建CGContext失败"])
        }
        
        let pixelData = context.data!.bindMemory(to: UInt8.self, capacity: width * height * bytesPerPixel)
        data.copyBytes(to: pixelData, count: min(data.count, width * height * bytesPerPixel))
        
        guard let cgImage = context.makeImage() else {
            throw NSError(domain: "CreateUIImage", code: -1, userInfo: [NSLocalizedDescriptionKey: "创建CGImage失败"])
        }
        
        return UIImage(cgImage: cgImage)
    }
    
    /// 保存图像到相册（原始代码功能）
    private func saveImageToGallery(_ image: UIImage, width: Int, height: Int) {
        let currentTime = Date().timeIntervalSince1970
        if currentTime - lastSaveTime < PoseLandmarkerPlugin.saveInterval {
            return
        }
        
        do {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyyMMdd_HHmmss"
            let fileName = "POSE_\(formatter.string(from: Date()))_\(width)_\(height).jpg"
            
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let posesDir = documentsPath.appendingPathComponent("poses")
            
            if !FileManager.default.fileExists(atPath: posesDir.path) {
                try FileManager.default.createDirectory(at: posesDir, withIntermediateDirectories: true, attributes: nil)
            }
            
            let imageFile = posesDir.appendingPathComponent(fileName)
            
            if let imageData = image.jpegData(compressionQuality: 1.0) {
                try imageData.write(to: imageFile)
                
                print("\(PoseLandmarkerPlugin.tag): 图片已保存: \(imageFile.path)")
                lastSaveTime = currentTime
            }
            
        } catch {
            print("\(PoseLandmarkerPlugin.tag): 保存图片失败: \(error)")
        }
    }
}

// MARK: - PoseLandmarkerLiveStreamDelegate

extension PoseLandmarkerPlugin: PoseLandmarkerLiveStreamDelegate {
    public func poseLandmarker(_ poseLandmarker: PoseLandmarker, 
                               didFinishDetection result: PoseLandmarkerResult?, 
                               timestampInMilliseconds: Int, 
                               error: Error?) {
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self, let channel = self.channel else { return }
            
            if let error = error {
                let errorMsg = "MediaPipe检测错误: \(error.localizedDescription)"
                print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
                channel.invokeMethod("onError", arguments: errorMsg)
                return
            }
            
            guard let result = result else {
                print("\(PoseLandmarkerPlugin.tag): 检测结果为空，返回空列表")
                channel.invokeMethod("onResult", arguments: [])
                return
            }
            
            do {
                // 符合原始APK数据结构 - 返回 List<List<PoseLandmark>>
                var posesData: [[[String: Any]]] = []
                
                for (poseIndex, landmarks) in result.landmarks.enumerated() {
                    var posePoints: [[String: Any]] = []
                    
                    for (landmarkIndex, landmark) in landmarks.enumerated() {
                        let point: [String: Any] = [
                            "x": landmark.x,
                            "y": landmark.y, 
                            "z": landmark.z,
                            "visibility": landmark.visibility?.floatValue ?? 1.0,
                            "presence": landmark.presence?.floatValue ?? 1.0
                        ]
                        posePoints.append(point)
                    }
                    
                    posesData.append(posePoints)
                    print("\(PoseLandmarkerPlugin.tag): 第\(poseIndex + 1)个人检测到\(posePoints.count)个关键点")
                }
                
                print("\(PoseLandmarkerPlugin.tag): 姿势检测成功，共检测到\(posesData.count)个人")
                channel.invokeMethod("onResult", arguments: posesData)
                
            } catch {
                let errorMsg = "处理检测结果失败: \(error.localizedDescription)"
                print("\(PoseLandmarkerPlugin.tag): \(errorMsg)")
                channel.invokeMethod("onError", arguments: errorMsg)
            }
        }
    }
}