{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "716,832,939,1046,1129,1234,1350,1440,1526,1617,1710,1804,1898,1998,2091,2186,2280,2371,2462,2546,2655,2759,2857,2967,3067,3174,3333,8447", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "827,934,1041,1124,1229,1345,1435,1521,1612,1705,1799,1893,1993,2086,2181,2275,2366,2457,2541,2650,2754,2852,2962,3062,3169,3328,3427,8524"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,181,264,339,482,651,743", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "176,259,334,477,646,738,825"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4164,8146,8229,8304,8630,8799,8891", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "4235,8224,8299,8442,8794,8886,8973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,237", "endColumns": "85,95,90", "endOffsets": "136,232,323"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6826,6912,7008", "endColumns": "85,95,90", "endOffsets": "6907,7003,7094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,191,254,320,396,465,554,640", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "126,186,249,315,391,460,549,635,705"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6118,6194,6254,6317,6383,6459,6528,6617,6703", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "6189,6249,6312,6378,6454,6523,6612,6698,6768"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,488,666,750,833,914,1007,1099,1162,1224,1313,1404,1475,1545,1606,1672,1761,1853,1940,2011,2090,2160,2225,2319,2409,2476,2544,2597,2655,2702,2763,2823,2890,2951,3016,3075,3140,3209,3272,3339,3393,3450,3521,3592", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,88,91,86,70,78,69,64,93,89,66,67,52,57,46,60,59,66,60,64,58,64,68,62,66,53,56,70,70,51", "endOffsets": "282,483,661,745,828,909,1002,1094,1157,1219,1308,1399,1470,1540,1601,1667,1756,1848,1935,2006,2085,2155,2220,2314,2404,2471,2539,2592,2650,2697,2758,2818,2885,2946,3011,3070,3135,3204,3267,3334,3388,3445,3516,3587,3639"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,538,4240,4324,4407,4488,4581,4673,4736,4798,4887,4978,5049,5119,5180,5246,5335,5427,5514,5585,5664,5734,5799,5893,5983,6050,6773,7099,7157,7204,7265,7325,7392,7453,7518,7577,7642,7711,7774,7841,7895,7952,8023,8094", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,88,91,86,70,78,69,64,93,89,66,67,52,57,46,60,59,66,60,64,58,64,68,62,66,53,56,70,70,51", "endOffsets": "332,533,711,4319,4402,4483,4576,4668,4731,4793,4882,4973,5044,5114,5175,5241,5330,5422,5509,5580,5659,5729,5794,5888,5978,6045,6113,6821,7152,7199,7260,7320,7387,7448,7513,7572,7637,7706,7769,7836,7890,7947,8018,8089,8141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3432,3534,3637,3742,3847,3946,4050,8529", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "3529,3632,3737,3842,3941,4045,4159,8625"}}]}]}