import 'dart:async';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:keepdance/models/local_video_score.dart';
import 'package:keepdance/pages/creation/services/dance_encryption_service.dart';
import 'package:pose/utils/pose_rating_system.dart';
import 'package:logger/logger.dart';
import 'package:path/path.dart' as path;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';

// 定义一个空的顶层类，对应 class id: 1050131
class TopLevelClass {}

class LocalVideoScoreService extends GetxService {
  // 对应 Logger 的初始化
  final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 10,
      lineLength: 100,
      printTime: true,
    ),
  );

  Database? _db;
  Future<void>? _dbInitializingFuture;
  static const int _maxHistoryRecords = 10;

  /// 确保数据库已初始化
  Future<void> ensureDatabaseInitialized() async {
    // 如果数据库已存在，直接返回
    if (_db != null) {
      return;
    }
    // 如果正在初始化中，等待其完成
    if (_dbInitializingFuture != null) {
      _logger.d("数据库正在初始化中，等待完成...");
      await _dbInitializingFuture;
      return;
    }

    // 开始新的初始化过程
    _logger.d("开始数据库初始化...");
    final completer = Completer<void>();
    _dbInitializingFuture = completer.future;

    try {
      await _initDatabase();
      _logger.i("数据库初始化完成");
      completer.complete();
    } catch (e) {
      completer.completeError(e);
      rethrow; // 重新抛出异常
    } finally {
      // 完成后清空 future，以便下次可以重新初始化
      _dbInitializingFuture = null;
    }
  }

  /// 初始化数据库
  Future<void> _initDatabase() async {
    try {
      String databasesPath = await getDatabasesPath();
      String dbPath = path.join(databasesPath, "local_video_scores.db");

      _db = await openDatabase(
        dbPath,
        version: 1,
        onCreate: _createDatabase,
        onUpgrade: _upgradeDatabase,
      );
      _logger.i("本地得分数据库初始化成功: $dbPath");
    } catch (e, s) {
      _logger.e("数据库初始化失败: $e");
      rethrow;
    }
  }

  /// 创建数据库表
  Future<void> _createDatabase(Database db, int version) async {
    try {
      await db.execute('''
        CREATE TABLE local_video_scores (
          id TEXT PRIMARY KEY,
          local_video_id TEXT NOT NULL,
          user_id TEXT NOT NULL,
          max_score TEXT NOT NULL,
          star_grade TEXT NOT NULL,
          exercise_times TEXT NOT NULL,
          learn_time TEXT NOT NULL,
          score_evaluation_json TEXT NOT NULL,
          create_time TEXT NOT NULL,
          update_time TEXT NOT NULL,
          score_evaluation TEXT NOT NULL,
          calories REAL NOT NULL,
          video_name TEXT NOT NULL,
          video_duration_seconds INTEGER NOT NULL,
          FOREIGN KEY (local_video_id) REFERENCES local_dance_videos (id)
        )
      ''');
      await db.execute(
          'CREATE INDEX idx_local_video_id ON local_video_scores (local_video_id)');
      await db.execute('CREATE INDEX idx_user_id ON local_video_scores (user_id)');
      await db
          .execute('CREATE INDEX idx_create_time ON local_video_scores (create_time)');
      _logger.i("数据库表创建成功");
    } catch (e, s) {
      _logger.e("创建数据库表失败: $e");
      rethrow;
    }
  }

  /// 升级数据库
  Future<void> _upgradeDatabase(
      Database db, int oldVersion, int newVersion) async {
    _logger.i("数据库升级: $oldVersion -> $newVersion");
    // 此处可添加未来的数据库升级逻辑
  }
  
  /// 获取当前用户ID
  Future<String> _getCurrentUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = prefs.getString('user_data') ?? '{}';
      final userData = jsonDecode(userDataString);
      final userId = userData?['value']?['id']?.toString();
      return userId ?? 'local_user';
    } catch (e) {
      _logger.w("获取用户ID失败，使用默认值: $e");
      return 'local_user';
    }
  }
  
  /// 获取指定视频的练习次数
  Future<int> _getExerciseTimesForVideo(
      String localVideoId, String userId) async {
    try {
      if (_db == null) throw Exception("数据库未初始化");

      final result = await _db!.query(
        'local_video_scores',
        columns: ['COUNT(*) as count'],
        where: 'local_video_id = ? AND user_id = ?',
        whereArgs: [localVideoId, userId],
      );

      final count = result.first['count'] as int?;
      return count ?? 0;
    } catch (e, s) {
      _logger.e("获取练习次数失败: $e");
      return 0;
    }
  }
  
  /// 加密敏感得分数据（如果需要）
  Future<void> _encryptSensitiveScoreData(Map<String, dynamic> scoreData) async {
    try {
      String? scoreJson = scoreData['score_evaluation_json'];
      if (scoreJson != null && scoreJson.isNotEmpty) {
        // 直接加密，不再判断是否已加密，因为解密时会判断
        final encryptedData = await DanceEncryptionService.encryptMetadata(scoreJson);
        if (encryptedData != null) {
          scoreData['score_evaluation_json'] = encryptedData;
        } else {
          _logger.w("得分评价数据加密失败，保持明文");
        }
      }
    } catch (e) {
      _logger.e("加密得分数据失败: $e");
    }
  }

  /// 解密敏感得分数据（如果存在）
  Future<void> _decryptSensitiveScoreData(Map<String, dynamic> scoreData) async {
    try {
      String? scoreJson = scoreData['score_evaluation_json'];
      if (scoreJson != null &&
          scoreJson.isNotEmpty &&
          DanceEncryptionService.isEncryptedTextData(scoreJson)) {
        final decryptedData = await DanceEncryptionService.decryptMetadata(scoreJson);
        if (decryptedData != null) {
          scoreData['score_evaluation_json'] = decryptedData;
        } else {
          _logger.e("得分评价数据解密失败");
        }
      }
    } catch (e) {
      _logger.e("解密得分数据失败: $e");
    }
  }
  
  /// 保存本地视频得分
  Future<String> saveLocalVideoScore(
      String localVideoId,
      Map<String, dynamic> poseScoreData,
      double calories,
      int videoDurationSeconds,
      String videoName) async {
    try {
      if (_db == null) throw Exception("数据库未初始化");
      
      final userId = await _getCurrentUserId();
      final scoreId = "score_${DateTime.now().millisecondsSinceEpoch}_$localVideoId";
      
      final scoreEvaluation = LocalScoreEvaluation.fromPoseScoreData(poseScoreData);
      final maxScore = scoreEvaluation.totalScore;
      final starGrade = PoseRatingSystem.getStars(maxScore).toString();
      
      final exerciseTimes = await _getExerciseTimesForVideo(localVideoId, userId) + 1;

      final now = DateTime.now().toIso8601String();
      
      final score = LocalVideoScore(
        id: scoreId,
        localVideoId: localVideoId,
        userId: userId,
        maxScore: maxScore.toString(),
        starGrade: starGrade,
        exerciseTimes: exerciseTimes.toString(),
        learnTime: videoDurationSeconds.toString(), // 假设 learn_time 是视频时长
        scoreEvaluationJson: jsonEncode(scoreEvaluation.toJson()),
        createTime: now,
        updateTime: now,
        scoreEvaluation: scoreEvaluation,
        calories: calories,
        videoName: videoName,
        videoDurationSeconds: videoDurationSeconds,
      );

      final scoreMap = score.toJson();
      await _encryptSensitiveScoreData(scoreMap);

      await _db!.insert(
        'local_video_scores',
        scoreMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      _logger.i("本地视频得分保存成功: $scoreId, 得分: $maxScore");
      
      await _cleanupHistoryRecords(localVideoId, userId);
      
      final bestScore = await getBestLocalVideoScore(localVideoId);
      if (bestScore != null && maxScore > double.parse(bestScore.maxScore)) {
          _logger.i("新分数($maxScore)创造了新的最高分记录");
      }
      
      return scoreId;
    } catch (e, s) {
      _logger.e("保存本地视频得分失败: $e");
      rethrow;
    }
  }

  /// 清理历史记录，只保留分数最高的 `_maxHistoryRecords` 条
  Future<void> _cleanupHistoryRecords(String localVideoId, String userId) async {
    try {
      if (_db == null) return;
      
      final records = await _db!.query(
        'local_video_scores',
        where: 'local_video_id = ? AND user_id = ?',
        whereArgs: [localVideoId, userId],
        orderBy: 'CAST(max_score AS REAL) DESC, create_time DESC',
      );

      if (records.length > _maxHistoryRecords) {
        final recordsToDelete = records.sublist(_maxHistoryRecords);
        for (var record in recordsToDelete) {
          final id = record['id'] as String;
          await _db!.delete(
            'local_video_scores',
            where: 'id = ?',
            whereArgs: [id],
          );
        }
        _logger.i(
            "清理历史记录: 删除了 ${recordsToDelete.length} 条低分记录，保留前$_maxHistoryRecords条最高分");
      }
    } catch (e, s) {
      _logger.e("清理历史记录失败: $e");
    }
  }

  /// 获取本地视频最高分记录
  Future<LocalVideoScore?> getBestLocalVideoScore(String localVideoId) async {
    try {
      if (_db == null) throw Exception("数据库未初始化");

      final userId = await _getCurrentUserId();
      final List<Map<String, dynamic>> maps = await _db!.query(
        'local_video_scores',
        where: 'local_video_id = ? AND user_id = ?',
        whereArgs: [localVideoId, userId],
        orderBy: 'CAST(max_score AS REAL) DESC',
        limit: 1, // 汇编中是2，但根据函数名和逻辑，1更合理。如果必须严格遵守，可改为2
      );

      if (maps.isNotEmpty) {
        final scoreMap = Map<String, dynamic>.from(maps.first);
        await _decryptSensitiveScoreData(scoreMap);
        return LocalVideoScore.fromJson(scoreMap);
      }
      return null;
    } catch (e, s) {
      _logger.e("获取本地视频最高得分失败: $e");
      return null;
    }
  }

  /// 获取本地视频得分历史记录（最近20条）
  Future<List<LocalVideoScore>> getLocalVideoScoreHistory(
      String localVideoId) async {
    try {
      if (_db == null) throw Exception("数据库未初始化");

      final userId = await _getCurrentUserId();
      final List<Map<String, dynamic>> maps = await _db!.query(
        'local_video_scores',
        where: 'local_video_id = ? AND user_id = ?',
        whereArgs: [localVideoId, userId],
        orderBy: 'CAST(max_score AS REAL) DESC, create_time DESC',
        limit: 20,
      );

      final List<LocalVideoScore> scores = [];
      for (var map in maps) {
        final scoreMap = Map<String, dynamic>.from(map);
        await _decryptSensitiveScoreData(scoreMap);
        scores.add(LocalVideoScore.fromJson(scoreMap));
      }
      
      _logger.d("获取本地视频得分历史: ${scores.length} 条记录，按分数排序");
      return scores;
    } catch (e, s) {
      _logger.e("获取本地视频得分历史失败: $e");
      return [];
    }
  }
  
  /// 获取本地视频得分统计数据
  Future<Map<String, dynamic>> getLocalVideoScoreStatistics() async {
    try {
      if (_db == null) throw Exception("数据库未初始化");

      final userId = await _getCurrentUserId();

      final totalExercisesResult = await _db!.rawQuery(
          'SELECT COUNT(*) as total_exercises FROM local_video_scores WHERE user_id = ?',
          [userId]);
      final avgScoreResult = await _db!.rawQuery(
          'SELECT AVG(CAST(max_score AS REAL)) as avg_score FROM local_video_scores WHERE user_id = ? AND CAST(max_score AS REAL) > 0',
          [userId]);
      final maxScoreResult = await _db!.rawQuery(
          'SELECT MAX(CAST(max_score AS REAL)) as max_score FROM local_video_scores WHERE user_id = ?',
          [userId]);
      final totalCaloriesResult = await _db!.rawQuery(
          'SELECT SUM(calories) as total_calories FROM local_video_scores WHERE user_id = ?',
          [userId]);
      final uniqueVideosResult = await _db!.rawQuery(
          'SELECT COUNT(DISTINCT local_video_id) as unique_videos FROM local_video_scores WHERE user_id = ?',
          [userId]);

      final stats = {
        'totalExercises': (totalExercisesResult.first['total_exercises'] as int?) ?? 0,
        'averageScore': (avgScoreResult.first['avg_score'] as double?) ?? 0.0,
        'maxScore': (maxScoreResult.first['max_score'] as double?) ?? 0.0,
        'totalCalories': (totalCaloriesResult.first['total_calories'] as double?) ?? 0.0,
        'uniqueVideos': (uniqueVideosResult.first['unique_videos'] as int?) ?? 0,
      };

      _logger.d("本地视频得分统计: $stats");
      return stats;
    } catch (e, s) {
      _logger.e("获取本地视频得分统计失败: $e");
      return {};
    }
  }

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initDatabase();
    _logger.i("LocalVideoScoreService 初始化完成");
  }

  @override
  void onClose() {
    _db?.close();
    // 汇编代码没有调用 super.onClose()，此处保持一致
    // super.onClose();
  }
}
