{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,510,738,826,915,997,1080,1172,1269,1335,1431,1527,1592,1662,1727,1801,1880,1960,2049,2119,2202,2274,2371,2465,2556,2622,2697,2750,2808,2862,2923,2988,3057,3122,3194,3256,3316,3381,3448,3515,3573,3639,3719,3799", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,88,81,82,91,96,65,95,95,64,69,64,73,78,79,88,69,82,71,96,93,90,65,74,52,57,53,60,64,68,64,71,61,59,64,66,66,57,65,79,79,53", "endOffsets": "282,505,733,821,910,992,1075,1167,1264,1330,1426,1522,1587,1657,1722,1796,1875,1955,2044,2114,2197,2269,2366,2460,2551,2617,2692,2745,2803,2857,2918,2983,3052,3117,3189,3251,3311,3376,3443,3510,3568,3634,3714,3794,3848"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,560,4377,4465,4554,4636,4719,4811,4908,4974,5070,5166,5231,5301,5366,5440,5519,5599,5688,5758,5841,5913,6010,6104,6195,6261,6997,7310,7368,7422,7483,7548,7617,7682,7754,7816,7876,7941,8008,8075,8133,8199,8279,8359", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,87,88,81,82,91,96,65,95,95,64,69,64,73,78,79,88,69,82,71,96,93,90,65,74,52,57,53,60,64,68,64,71,61,59,64,66,66,57,65,79,79,53", "endOffsets": "332,555,783,4460,4549,4631,4714,4806,4903,4969,5065,5161,5226,5296,5361,5435,5514,5594,5683,5753,5836,5908,6005,6099,6190,6256,6331,7045,7363,7417,7478,7543,7612,7677,7749,7811,7871,7936,8003,8070,8128,8194,8274,8354,8408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,138,222", "endColumns": "82,83,92", "endOffsets": "133,217,310"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "7050,7133,7217", "endColumns": "82,83,92", "endOffsets": "7128,7212,7305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,265,348,490,659,744", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "173,260,343,485,654,739,819"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4304,8413,8500,8583,8912,9081,9166", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "4372,8495,8578,8720,9076,9161,9241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "788,906,1017,1134,1219,1325,1448,1537,1622,1713,1806,1901,1995,2095,2188,2283,2380,2471,2562,2647,2758,2867,2969,3080,3190,3298,3469,8725", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "901,1012,1129,1214,1320,1443,1532,1617,1708,1801,1896,1990,2090,2183,2278,2375,2466,2557,2642,2753,2862,2964,3075,3185,3293,3464,3564,8806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,184,242,305,379,455,554,649", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "121,179,237,300,374,450,549,644,711"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6336,6407,6465,6523,6586,6660,6736,6835,6930", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "6402,6460,6518,6581,6655,6731,6830,6925,6992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3569,3667,3770,3870,3973,4081,4187,8811", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3662,3765,3865,3968,4076,4182,4299,8907"}}]}]}