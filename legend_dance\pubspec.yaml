name: keepdance
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # 状态管理 - GetX框架
  get: ^4.6.6
  
  # 日志记录
  logger: ^2.0.2+1
  
  # 屏幕适配
  flutter_screenutil: ^5.9.0
  
  # 本地存储
  shared_preferences: ^2.2.2
  
  # UI组件相关
  cupertino_icons: ^1.0.6
  
  # HTTP网络请求
  http: ^1.1.0
  
  # JSON处理
  json_annotation: ^4.8.1
  
  # 图片缓存和加载
  cached_network_image: ^3.3.0
  
  # 权限管理
  permission_handler: ^11.0.1
  
  # 设备信息
  device_info_plus: ^9.1.1
  
  # 文件操作相关
  path: ^1.8.3
  path_provider: ^2.1.1
  # file_picker: ^6.1.1  # v1 embedding兼容性问题，暂时注释
  
  # 加密相关
  crypto: ^3.0.3
  
  # 媒体处理
  video_player: ^2.8.1
  image: 4.5.4
  
  # JSON序列化
  json_serializable: ^6.7.1
  package_info_plus: ^8.3.1

  connectivity_plus: ^6.1.0
  pull_to_refresh: 2.0.0
  hugeicons: 0.0.11
  async: 2.13.0
  encrypt: 5.0.3
  lpinyin: 2.0.3
  get_storage: 2.1.1

  better_player: ^0.0.84
  palette_generator: ^0.3.3+5

  camera: 0.11.0
  fluwx: 5.7.2
  share_plus: 11.1.0
  video_thumbnail: 0.5.6

  # 姿势检测插件 - 本地插件
  pose:
    path: plugins/pose_landmarker_plugin

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  
  # JSON序列化代码生成
  build_runner: ^2.4.7

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/animations/
    # Source: libapp.so contains "assets/images/empty_state_myWork.png"
    # indicating original app uses similar asset structure

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# 修复第三方库兼容性问题
dependency_overrides:
  # 修复hashValues兼容性问题 - 使用本地修复版本
  better_player:
    path: lib/packages/better_player
  palette_generator:
    path: lib/packages/palette_generator
  # 强制使用兼容版本
  meta: ^1.9.1
  flutter:
    sdk: flutter
