{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,138,218", "endColumns": "82,79,77", "endOffsets": "133,213,291"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6670,6753,6833", "endColumns": "82,79,77", "endOffsets": "6748,6828,6906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,479,648,728", "endColumns": "69,85,79,137,168,79,77", "endOffsets": "170,256,336,474,643,723,801"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4078,7971,8057,8137,8458,8627,8707", "endColumns": "69,85,79,137,168,79,77", "endOffsets": "4143,8052,8132,8270,8622,8702,8780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "705,808,911,1024,1109,1213,1324,1402,1479,1570,1663,1755,1849,1949,2042,2137,2233,2324,2415,2496,2603,2707,2805,2908,3012,3116,3273,8275", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "803,906,1019,1104,1208,1319,1397,1474,1565,1658,1750,1844,1944,2037,2132,2228,2319,2410,2491,2598,2702,2800,2903,3007,3111,3268,3367,8352"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,185,251,316,391,461,553,640", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "119,180,246,311,386,456,548,635,707"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5960,6029,6090,6156,6221,6296,6366,6458,6545", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "6024,6085,6151,6216,6291,6361,6453,6540,6612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3372,3468,3571,3670,3768,3869,3967,8357", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "3463,3566,3665,3763,3864,3962,4073,8453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,477,655,737,817,894,982,1064,1140,1204,1297,1389,1459,1523,1586,1656,1735,1811,1885,1953,2030,2100,2176,2259,2342,2404,2467,2520,2578,2626,2687,2746,2814,2875,2941,3005,3064,3128,3195,3262,3316,3376,3450,3524", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,78,75,73,67,76,69,75,82,82,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "281,472,650,732,812,889,977,1059,1135,1199,1292,1384,1454,1518,1581,1651,1730,1806,1880,1948,2025,2095,2171,2254,2337,2399,2462,2515,2573,2621,2682,2741,2809,2870,2936,3000,3059,3123,3190,3257,3311,3371,3445,3519,3575"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,527,4148,4230,4310,4387,4475,4557,4633,4697,4790,4882,4952,5016,5079,5149,5228,5304,5378,5446,5523,5593,5669,5752,5835,5897,6617,6911,6969,7017,7078,7137,7205,7266,7332,7396,7455,7519,7586,7653,7707,7767,7841,7915", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,78,75,73,67,76,69,75,82,82,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "331,522,700,4225,4305,4382,4470,4552,4628,4692,4785,4877,4947,5011,5074,5144,5223,5299,5373,5441,5518,5588,5664,5747,5830,5892,5955,6665,6964,7012,7073,7132,7200,7261,7327,7391,7450,7514,7581,7648,7702,7762,7836,7910,7966"}}]}]}