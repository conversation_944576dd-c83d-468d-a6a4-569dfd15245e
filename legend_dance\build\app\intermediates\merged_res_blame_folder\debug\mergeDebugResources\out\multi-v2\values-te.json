{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,195,268,336,416,493,594,687", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "122,190,263,331,411,488,589,682,760"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6290,6362,6430,6503,6571,6651,6728,6829,6922", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "6357,6425,6498,6566,6646,6723,6824,6917,6995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "745,862,974,1087,1177,1282,1401,1479,1555,1646,1739,1834,1928,2028,2121,2216,2311,2402,2493,2582,2696,2800,2899,3014,3119,3234,3396,8722", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "857,969,1082,1172,1277,1396,1474,1550,1641,1734,1829,1923,2023,2116,2211,2306,2397,2488,2577,2691,2795,2894,3009,3114,3229,3391,3494,8800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,277,356,502,671,758", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "173,272,351,497,666,753,837"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4249,8398,8497,8576,8906,9075,9162", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "4317,8492,8571,8717,9070,9157,9241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,151,244", "endColumns": "95,92,93", "endOffsets": "146,239,333"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "7053,7149,7242", "endColumns": "95,92,93", "endOffsets": "7144,7237,7331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3499,3601,3709,3811,3912,4018,4125,8805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3596,3704,3806,3907,4013,4120,4244,8901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,502,695,787,879,969,1069,1171,1248,1313,1405,1497,1568,1638,1699,1769,1861,1950,2040,2115,2199,2274,2345,2433,2520,2584,2663,2716,2774,2822,2883,2950,3012,3077,3144,3203,3265,3331,3395,3462,3516,3576,3650,3724", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,91,89,99,101,76,64,91,91,70,69,60,69,91,88,89,74,83,74,70,87,86,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "281,497,690,782,874,964,1064,1166,1243,1308,1400,1492,1563,1633,1694,1764,1856,1945,2035,2110,2194,2269,2340,2428,2515,2579,2658,2711,2769,2817,2878,2945,3007,3072,3139,3198,3260,3326,3390,3457,3511,3571,3645,3719,3773"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,552,4322,4414,4506,4596,4696,4798,4875,4940,5032,5124,5195,5265,5326,5396,5488,5577,5667,5742,5826,5901,5972,6060,6147,6211,7000,7336,7394,7442,7503,7570,7632,7697,7764,7823,7885,7951,8015,8082,8136,8196,8270,8344", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,91,91,89,99,101,76,64,91,91,70,69,60,69,91,88,89,74,83,74,70,87,86,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "331,547,740,4409,4501,4591,4691,4793,4870,4935,5027,5119,5190,5260,5321,5391,5483,5572,5662,5737,5821,5896,5967,6055,6142,6206,6285,7048,7389,7437,7498,7565,7627,7692,7759,7818,7880,7946,8010,8077,8131,8191,8265,8339,8393"}}]}]}