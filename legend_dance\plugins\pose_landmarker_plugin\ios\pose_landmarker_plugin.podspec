#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint pose_landmarker_plugin.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'pose_landmarker_plugin'
  s.version          = '1.0.0'
  s.summary          = 'A production-grade Flutter plugin for pose detection using MediaPipe.'
  s.description      = <<-DESC
A Flutter plugin that provides pose detection capabilities using Google MediaPipe Pose Landmarker.
Supports real-time pose detection with multi-person tracking, optimized for production use.
Compatible with both YUV420 and RGBA image formats.
                       DESC
  s.homepage         = 'https://github.com/yourcompany/pose_landmarker_plugin'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'JXHY' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  
  # Flutter dependency
  s.dependency 'Flutter'
  
  # MediaPipe dependencies - matching Android version 0.10.8
  s.dependency 'MediaPipeTasksVision', '0.10.8'
  
  # iOS deployment target - iOS 12.0 minimum for MediaPipe
  s.platform = :ios, '12.0'

  # Build configurations
  s.pod_target_xcconfig = { 
    'DEFINES_MODULE' => 'YES', 
    'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386',
    'OTHER_LDFLAGS' => '-framework Accelerate -framework CoreImage -framework CoreVideo'
  }
  
  # Swift version
  s.swift_version = '5.0'
  
  # Resource files
  s.resource_bundles = {
    'pose_landmarker_plugin' => ['Assets/*.task']
  }
end