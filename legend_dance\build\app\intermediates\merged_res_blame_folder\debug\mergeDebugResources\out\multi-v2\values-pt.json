{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3438,3535,3637,3736,3836,3943,4053,8586", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3530,3632,3731,3831,3938,4048,4168,8682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,650,731,814,887,986,1082,1156,1222,1318,1413,1479,1548,1615,1686,1763,1839,1915,1982,2068,2144,2218,2310,2398,2462,2526,2579,2637,2685,2746,2811,2873,2939,3009,3073,3134,3200,3265,3331,3384,3448,3526,3604", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,76,75,75,66,85,75,73,91,87,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "280,466,645,726,809,882,981,1077,1151,1217,1313,1408,1474,1543,1610,1681,1758,1834,1910,1977,2063,2139,2213,2305,2393,2457,2521,2574,2632,2680,2741,2806,2868,2934,3004,3068,3129,3195,3260,3326,3379,3443,3521,3599,3658"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,4243,4324,4407,4480,4579,4675,4749,4815,4911,5006,5072,5141,5208,5279,5356,5432,5508,5575,5661,5737,5811,5903,5991,6055,6805,7099,7157,7205,7266,7331,7393,7459,7529,7593,7654,7720,7785,7851,7904,7968,8046,8124", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,76,75,75,66,85,75,73,91,87,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "330,516,695,4319,4402,4475,4574,4670,4744,4810,4906,5001,5067,5136,5203,5274,5351,5427,5503,5570,5656,5732,5806,5898,5986,6050,6114,6853,7152,7200,7261,7326,7388,7454,7524,7588,7649,7715,7780,7846,7899,7963,8041,8119,8178"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "700,820,926,1033,1122,1223,1342,1427,1507,1598,1691,1786,1880,1980,2073,2168,2263,2354,2445,2530,2637,2748,2850,2958,3066,3176,3338,8500", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "815,921,1028,1117,1218,1337,1422,1502,1593,1686,1781,1875,1975,2068,2163,2258,2349,2440,2525,2632,2743,2845,2953,3061,3171,3333,3433,8581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4173,8183,8270,8349,8687,8856,8943", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "4238,8265,8344,8495,8851,8938,9019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6119,6189,6259,6331,6397,6474,6541,6642,6735", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "6184,6254,6326,6392,6469,6536,6637,6730,6800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,216", "endColumns": "80,79,79", "endOffsets": "131,211,291"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6858,6939,7019", "endColumns": "80,79,79", "endOffsets": "6934,7014,7094"}}]}]}