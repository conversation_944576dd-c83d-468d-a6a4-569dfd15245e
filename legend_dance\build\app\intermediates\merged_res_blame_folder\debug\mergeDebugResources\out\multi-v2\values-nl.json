{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3449,3551,3653,3753,3853,3960,4064,8539", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3546,3648,3748,3848,3955,4059,4178,8635"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,486,677,766,854,934,1027,1120,1193,1260,1362,1460,1528,1595,1660,1729,1808,1887,1964,2037,2116,2191,2260,2337,2413,2479,2544,2597,2655,2703,2764,2829,2891,2956,3024,3082,3140,3206,3271,3337,3389,3451,3527,3603", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,78,78,76,72,78,74,68,76,75,65,64,52,57,47,60,64,61,64,67,57,57,65,64,65,51,61,75,75,54", "endOffsets": "281,481,672,761,849,929,1022,1115,1188,1255,1357,1455,1523,1590,1655,1724,1803,1882,1959,2032,2111,2186,2255,2332,2408,2474,2539,2592,2650,2698,2759,2824,2886,2951,3019,3077,3135,3201,3266,3332,3384,3446,3522,3598,3653"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,536,4255,4344,4432,4512,4605,4698,4771,4838,4940,5038,5106,5173,5238,5307,5386,5465,5542,5615,5694,5769,5838,5915,5991,6057,6778,7078,7136,7184,7245,7310,7372,7437,7505,7563,7621,7687,7752,7818,7870,7932,8008,8084", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,78,78,76,72,78,74,68,76,75,65,64,52,57,47,60,64,61,64,67,57,57,65,64,65,51,61,75,75,54", "endOffsets": "331,531,722,4339,4427,4507,4600,4693,4766,4833,4935,5033,5101,5168,5233,5302,5381,5460,5537,5610,5689,5764,5833,5910,5986,6052,6117,6826,7131,7179,7240,7305,7367,7432,7500,7558,7616,7682,7747,7813,7865,7927,8003,8079,8134"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,267,348,494,663,743", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "172,262,343,489,658,738,815"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4183,8139,8229,8310,8640,8809,8889", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "4250,8224,8305,8451,8804,8884,8961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,138,221", "endColumns": "82,82,80", "endOffsets": "133,216,297"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6831,6914,6997", "endColumns": "82,82,80", "endOffsets": "6909,6992,7073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,254,321,398,467,556,639", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "121,185,249,316,393,462,551,634,706"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6122,6193,6257,6321,6388,6465,6534,6623,6706", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "6188,6252,6316,6383,6460,6529,6618,6701,6773"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "727,845,950,1057,1142,1246,1366,1444,1520,1612,1706,1801,1895,1995,2089,2185,2280,2372,2464,2546,2657,2760,2859,2974,3088,3191,3346,8456", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "840,945,1052,1137,1241,1361,1439,1515,1607,1701,1796,1890,1990,2084,2180,2275,2367,2459,2541,2652,2755,2854,2969,3083,3186,3341,3444,8534"}}]}]}