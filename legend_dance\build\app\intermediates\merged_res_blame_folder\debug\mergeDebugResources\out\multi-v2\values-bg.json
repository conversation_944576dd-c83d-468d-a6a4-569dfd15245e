{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "756,876,982,1087,1173,1283,1404,1484,1561,1652,1745,1840,1934,2034,2127,2222,2330,2421,2512,2595,2709,2817,2917,3031,3138,3246,3406,8704", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "871,977,1082,1168,1278,1399,1479,1556,1647,1740,1835,1929,2029,2122,2217,2325,2416,2507,2590,2704,2812,2912,3026,3133,3241,3401,3500,8783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,225", "endColumns": "87,81,93", "endOffsets": "138,220,314"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "7024,7112,7194", "endColumns": "87,81,93", "endOffsets": "7107,7189,7283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,189,253,327,405,478,575,666", "endColumns": "70,62,63,73,77,72,96,90,75", "endOffsets": "121,184,248,322,400,473,570,661,737"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6283,6354,6417,6481,6555,6633,6706,6803,6894", "endColumns": "70,62,63,73,77,72,96,90,75", "endOffsets": "6349,6412,6476,6550,6628,6701,6798,6889,6965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,345,485,654,736", "endColumns": "71,87,79,139,168,81,77", "endOffsets": "172,260,340,480,649,731,809"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4246,8396,8484,8564,8889,9058,9140", "endColumns": "71,87,79,139,168,81,77", "endOffsets": "4313,8479,8559,8699,9053,9135,9213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3505,3602,3712,3814,3915,4022,4127,8788", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "3597,3707,3809,3910,4017,4122,4241,8884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,500,706,796,887,970,1057,1145,1225,1290,1394,1499,1577,1651,1715,1783,1867,1945,2035,2112,2204,2276,2357,2448,2537,2603,2671,2725,2785,2833,2894,2966,3034,3097,3173,3238,3296,3367,3432,3503,3555,3614,3695,3776", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,89,90,82,86,87,79,64,103,104,77,73,63,67,83,77,89,76,91,71,80,90,88,65,67,53,59,47,60,71,67,62,75,64,57,70,64,70,51,58,80,80,56", "endOffsets": "282,495,701,791,882,965,1052,1140,1220,1285,1389,1494,1572,1646,1710,1778,1862,1940,2030,2107,2199,2271,2352,2443,2532,2598,2666,2720,2780,2828,2889,2961,3029,3092,3168,3233,3291,3362,3427,3498,3550,3609,3690,3771,3828"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,550,4318,4408,4499,4582,4669,4757,4837,4902,5006,5111,5189,5263,5327,5395,5479,5557,5647,5724,5816,5888,5969,6060,6149,6215,6970,7288,7348,7396,7457,7529,7597,7660,7736,7801,7859,7930,7995,8066,8118,8177,8258,8339", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,89,90,82,86,87,79,64,103,104,77,73,63,67,83,77,89,76,91,71,80,90,88,65,67,53,59,47,60,71,67,62,75,64,57,70,64,70,51,58,80,80,56", "endOffsets": "332,545,751,4403,4494,4577,4664,4752,4832,4897,5001,5106,5184,5258,5322,5390,5474,5552,5642,5719,5811,5883,5964,6055,6144,6210,6278,7019,7343,7391,7452,7524,7592,7655,7731,7796,7854,7925,7990,8061,8113,8172,8253,8334,8391"}}]}]}