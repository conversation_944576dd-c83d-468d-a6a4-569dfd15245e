{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3417,3516,3618,3716,3813,3921,4032,8544", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3511,3613,3711,3808,3916,4027,4149,8640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,485,671,752,833,919,1023,1115,1188,1251,1341,1431,1496,1559,1626,1694,1777,1860,1937,2004,2086,2158,2231,2315,2396,2460,2530,2583,2641,2689,2750,2815,2881,2943,3011,3075,3134,3200,3265,3331,3383,3448,3526,3604", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,82,82,76,66,81,71,72,83,80,63,69,52,57,47,60,64,65,61,67,63,58,65,64,65,51,64,77,77,56", "endOffsets": "281,480,666,747,828,914,1018,1110,1183,1246,1336,1426,1491,1554,1621,1689,1772,1855,1932,1999,2081,2153,2226,2310,2391,2455,2525,2578,2636,2684,2745,2810,2876,2938,3006,3070,3129,3195,3260,3326,3378,3443,3521,3599,3656"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,535,4223,4304,4385,4471,4575,4667,4740,4803,4893,4983,5048,5111,5178,5246,5329,5412,5489,5556,5638,5710,5783,5867,5948,6012,6752,7060,7118,7166,7227,7292,7358,7420,7488,7552,7611,7677,7742,7808,7860,7925,8003,8081", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,82,82,76,66,81,71,72,83,80,63,69,52,57,47,60,64,65,61,67,63,58,65,64,65,51,64,77,77,56", "endOffsets": "331,530,716,4299,4380,4466,4570,4662,4735,4798,4888,4978,5043,5106,5173,5241,5324,5407,5484,5551,5633,5705,5778,5862,5943,6007,6077,6800,7113,7161,7222,7287,7353,7415,7483,7547,7606,7672,7737,7803,7855,7920,7998,8076,8133"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,229", "endColumns": "86,86,80", "endOffsets": "137,224,305"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6805,6892,6979", "endColumns": "86,86,80", "endOffsets": "6887,6974,7055"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,254,320,398,477,569,655", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "121,182,249,315,393,472,564,650,720"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6082,6153,6214,6281,6347,6425,6504,6596,6682", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "6148,6209,6276,6342,6420,6499,6591,6677,6747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,266,350,498,667,751", "endColumns": "68,91,83,147,168,83,78", "endOffsets": "169,261,345,493,662,746,825"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4154,8138,8230,8314,8645,8814,8898", "endColumns": "68,91,83,147,168,83,78", "endOffsets": "4218,8225,8309,8457,8809,8893,8972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "721,835,935,1047,1133,1239,1362,1444,1522,1613,1706,1801,1895,1996,2089,2184,2281,2372,2465,2546,2652,2756,2854,2960,3064,3166,3320,8462", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "830,930,1042,1128,1234,1357,1439,1517,1608,1701,1796,1890,1991,2084,2179,2276,2367,2460,2541,2647,2751,2849,2955,3059,3161,3315,3412,8539"}}]}]}