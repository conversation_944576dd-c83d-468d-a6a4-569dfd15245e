{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,223", "endColumns": "80,86,78", "endOffsets": "131,218,297"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6859,6940,7027", "endColumns": "80,86,78", "endOffsets": "6935,7022,7101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,261,341,476,645,725", "endColumns": "68,86,79,134,168,79,75", "endOffsets": "169,256,336,471,640,720,796"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4154,8202,8289,8369,8687,8856,8936", "endColumns": "68,86,79,134,168,79,75", "endOffsets": "4218,8284,8364,8499,8851,8931,9007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "713,818,921,1030,1114,1219,1338,1416,1491,1583,1677,1770,1864,1965,2059,2156,2251,2343,2435,2516,2622,2729,2827,2931,3037,3144,3307,8504", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "813,916,1025,1109,1214,1333,1411,1486,1578,1672,1765,1859,1960,2054,2151,2246,2338,2430,2511,2617,2724,2822,2926,3032,3139,3302,3402,8581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,405,471,558,643", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "121,182,254,324,400,466,553,638,712"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6144,6215,6276,6348,6418,6494,6560,6647,6732", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "6210,6271,6343,6413,6489,6555,6642,6727,6801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3407,3505,3607,3706,3808,3917,4024,8586", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3500,3602,3701,3803,3912,4019,4149,8682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,663,749,837,916,1008,1100,1178,1243,1343,1441,1506,1574,1639,1710,1787,1870,1945,2015,2108,2183,2259,2355,2447,2516,2584,2637,2695,2743,2804,2878,2949,3012,3093,3151,3212,3278,3343,3409,3461,3523,3599,3675", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,76,82,74,69,92,74,75,95,91,68,67,52,57,47,60,73,70,62,80,57,60,65,64,65,51,61,75,75,57", "endOffsets": "281,470,658,744,832,911,1003,1095,1173,1238,1338,1436,1501,1569,1634,1705,1782,1865,1940,2010,2103,2178,2254,2350,2442,2511,2579,2632,2690,2738,2799,2873,2944,3007,3088,3146,3207,3273,3338,3404,3456,3518,3594,3670,3728"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,525,4223,4309,4397,4476,4568,4660,4738,4803,4903,5001,5066,5134,5199,5270,5347,5430,5505,5575,5668,5743,5819,5915,6007,6076,6806,7106,7164,7212,7273,7347,7418,7481,7562,7620,7681,7747,7812,7878,7930,7992,8068,8144", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,76,82,74,69,92,74,75,95,91,68,67,52,57,47,60,73,70,62,80,57,60,65,64,65,51,61,75,75,57", "endOffsets": "331,520,708,4304,4392,4471,4563,4655,4733,4798,4898,4996,5061,5129,5194,5265,5342,5425,5500,5570,5663,5738,5814,5910,6002,6071,6139,6854,7159,7207,7268,7342,7413,7476,7557,7615,7676,7742,7807,7873,7925,7987,8063,8139,8197"}}]}]}