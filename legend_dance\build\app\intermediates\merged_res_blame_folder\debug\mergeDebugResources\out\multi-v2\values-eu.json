{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,657,741,827,902,1001,1092,1187,1255,1351,1447,1514,1586,1651,1722,1805,1883,1962,2031,2120,2192,2287,2389,2487,2553,2620,2673,2731,2780,2841,2903,2975,3039,3106,3171,3235,3302,3368,3435,3489,3556,3637,3718", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,82,77,78,68,88,71,94,101,97,65,66,52,57,48,60,61,71,63,66,64,63,66,65,66,53,66,80,80,55", "endOffsets": "281,470,652,736,822,897,996,1087,1182,1250,1346,1442,1509,1581,1646,1717,1800,1878,1957,2026,2115,2187,2282,2384,2482,2548,2615,2668,2726,2775,2836,2898,2970,3034,3101,3166,3230,3297,3363,3430,3484,3551,3632,3713,3769"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,525,4254,4338,4424,4499,4598,4689,4784,4852,4948,5044,5111,5183,5248,5319,5402,5480,5559,5628,5717,5789,5884,5986,6084,6150,6915,7220,7278,7327,7388,7450,7522,7586,7653,7718,7782,7849,7915,7982,8036,8103,8184,8265", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,82,77,78,68,88,71,94,101,97,65,66,52,57,48,60,61,71,63,66,64,63,66,65,66,53,66,80,80,55", "endOffsets": "331,520,702,4333,4419,4494,4593,4684,4779,4847,4943,5039,5106,5178,5243,5314,5397,5475,5554,5623,5712,5784,5879,5981,6079,6145,6212,6963,7273,7322,7383,7445,7517,7581,7648,7713,7777,7844,7910,7977,8031,8098,8179,8260,8316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "707,816,914,1024,1110,1216,1340,1426,1507,1599,1693,1789,1883,1984,2078,2174,2271,2363,2456,2538,2647,2756,2855,2964,3071,3182,3353,8633", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "811,909,1019,1105,1211,1335,1421,1502,1594,1688,1784,1878,1979,2073,2169,2266,2358,2451,2533,2642,2751,2850,2959,3066,3177,3348,3447,8711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3452,3550,3653,3753,3856,3961,4064,8716", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3545,3648,3748,3851,3956,4059,4178,8812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,187,253,325,402,476,587,685", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "119,182,248,320,397,471,582,680,748"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6217,6286,6349,6415,6487,6564,6638,6749,6847", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "6281,6344,6410,6482,6559,6633,6744,6842,6910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,275,354,488,657,747", "endColumns": "70,98,78,133,168,89,83", "endOffsets": "171,270,349,483,652,742,826"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4183,8321,8420,8499,8817,8986,9076", "endColumns": "70,98,78,133,168,89,83", "endOffsets": "4249,8415,8494,8628,8981,9071,9155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,224", "endColumns": "86,81,82", "endOffsets": "137,219,302"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6968,7055,7137", "endColumns": "86,81,82", "endOffsets": "7050,7132,7215"}}]}]}