// lib: , url: package:keepdance/main.dart

// 导入必要的包
import 'dart:async';
import 'dart:ui'; // 用于 PlatformDispatcher 和 Locale
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // 用于 BasicMessageChannel, HardwareKeyboard, KeyEvent

// 导入全局hashValues函数，解决第三方包兼容性问题
import 'package:keepdance/utils/global_hash_values.dart';
// 导入foundation补丁，为第三方包提供hashValues函数
import 'package:keepdance/foundation_patch.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import 'package:keepdance/controllers/main_controller.dart';
import 'package:keepdance/core/network/api_client.dart';
import 'package:keepdance/pages/creation/controllers/my_works_controller.dart';
import 'package:keepdance/pages/creation/controllers/creation_controller.dart';
import 'package:keepdance/pages/creation/services/creation_preload_service.dart';
import 'package:keepdance/pages/creation/services/image_optimization_service.dart';
import 'package:keepdance/pages/creation/services/my_works_service.dart';
import 'package:keepdance/pages/home/<USER>/home_controller.dart';
// import 'package:keepdance/pages/message/controllers/message_controller.dart';
import 'package:keepdance/pages/video_import/services/video_import_record_service.dart';
import 'package:keepdance/pages/video_upload/services/local_storage_service.dart';
import 'package:pose/utils/pose_plugin_manager.dart';
import 'package:keepdance/routes/app_pages.dart';
import 'package:keepdance/services/cache_service.dart';
// import 'package:keepdance/services/clipboard_compliance_service.dart';
import 'package:keepdance/services/data_sync_service.dart';
import 'package:keepdance/services/error_classification_service.dart';
import 'package:keepdance/services/file_import_service.dart';
import 'package:keepdance/services/global_import_state_manager.dart';
import 'package:keepdance/services/local_database_service.dart';
import 'package:keepdance/services/local_video_score_service.dart';
import 'package:keepdance/services/network_service.dart';
// import 'package:keepdance/services/privacy_compliance_service.dart';
// import 'package:keepdance/services/recording_lifecycle_manager.dart';
// import 'package:keepdance/services/user_info_service.dart';
// import 'package:keepdance/utils/analytics/umeng_common_sdk.dart'; // 推测路径
// import 'package:keepdance/utils/channel_util.dart'; // 推测路径
import 'package:keepdance/utils/orientation_util.dart'; // 推测路径
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:keepdance/controllers/agreement_state_controller.dart'; // 推测路径

// 应用入口点
Future<void> main() async {
  await CustomApp.main();
}

// 顶层类
class CustomApp {
  static late final Logger logger = _createLogger();

  static Logger _createLogger() {
    return Logger(
      printer: PrettyPrinter(
        methodCount: 1,
        errorMethodCount: 8,
        lineLength: 120,
        printTime: true,
      ),
    );
  }

  static DateTime? _appPausedTime;

  static Future<void> main() async {
    try {
      // 核心初始化
      WidgetsFlutterBinding.ensureInitialized();
      await GetStorage.init();
      await Get.put(CacheService()).init();
      await Get.put(ApiClient(), permanent: true);
      // 获取并存储渠道信息
      // await _getChannelName();

      // 服务初始化
      // Get.put(PrivacyComplianceService(), permanent: true);
      // Get.put(ClipboardComplianceService(), permanent: true);
      Get.put(await LocalStorageService().init(), permanent: true);
      Get.put(FileImportService(), permanent: true);
      // Get.put(RecordingLifecycleManager(), permanent: true);

      // 安全初始化PosePluginManager，避免因插件问题导致应用崩溃
      try {
        final poseManager = PosePluginManager();
        await poseManager.initialize();
        Get.put(poseManager, permanent: true);
        logger.i('姿势检测插件初始化成功');
      } catch (e) {
        logger.e('姿势检测插件初始化失败: $e');
        // 创建一个空的PosePluginManager实例，避免后续代码出错
        Get.put(PosePluginManager(), permanent: true);
      }

      Get.put<bool>(false, tag: 'isInDebugMode', permanent: true);
      // 设置全局错误处理器
      FlutterError.onError = (FlutterErrorDetails details) {
        FlutterError.dumpErrorToConsole(details);
        logger.e('Flutter错误: ${details.stack}');
        logger.e('Flutter错误: ${details.stackFilter}');
        // 原始汇编有一段难以理解的代码，可能与特定事件处理有关，但主要功能是记录错误
        if (details.exception is! KeyUpEvent) {
          // 不做任何事
        }
      };
      // 追踪应用暂停/恢复事件
      _appPausedTime = DateTime.now();
      const MethodChannel('flutter/lifecycle').setMethodCallHandler((
        call,
      ) async {
        if (call.method == 'AppLifecycleState.paused') {
          final now = DateTime.now();
          if (_appPausedTime != null) {
            final duration = now.difference(_appPausedTime!).inSeconds;
            // 上报应用退出事件
            // UmengUtil.onEvent('app_exit', {'duration': duration});
          }
        } else if (call.method == 'AppLifecycleState.resumed') {
          _appPausedTime = DateTime.now();
        }
        return null;
      });
      logger.e('-------------------------------------------------------4');
      // 清理缓存
      await CacheManager(
        Config(
          'defaultImageCache',
          stalePeriod: const Duration(days: 7),
          maxNrOfCacheObjects: 200,
          repo: JsonCacheInfoRepository(databaseName: 'defaultImages'),
          fileService: HttpFileService(),
        ),
      ).emptyCache();

      // 重载应用 (仅在热重载时触发)
      // if (kDebugMode) {
      //    // BindingBase.reassembleApplication() 在 Release 模式下不存在
      //    // 原始汇编调用了它，这里用一个等效的检查
      // }

      // 设置屏幕方向
      await OrientationUtil.setPortrait();
      logger.e('--------------------------------------------------------5');
      // 依赖注入
      initDependencies();

      // 控制器初始化
      // Get.put(MessageController());
      Get.put(MainController(), permanent: true);
      // Get.put(AgreementStateController(), permanent: true);

      // 启动预加载服务
      try {
        Get.find<CreationPreloadService>().startPrewarmLoading();
        logger.d('创作页面预热加载已启动');
      } catch (e) {
        logger.e('预加载服务启动失败: $e');
        // 即使预加载失败，应用也应该继续运行
      }

      String initialRoute = Get.find<CacheService>().hasValidTokensSync()
          ? AppPages.MAIN
          : AppPages.LOGIN;

      // 异步操作
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      logger.e('--------------------------------------------------------6');
      // 设置平台错误处理器
      PlatformDispatcher.instance.onError = (error, stack) {
        logger.e('全局错误: $error');
        return true;
      };

      // 配置网络请求修改器
      GetConnect().httpClient.addResponseModifier((request, response) {
        // 原始汇编中此闭包为空，并抛出错误，表明可能在AOT编译中被移除。
        // 此处保留一个空的实现以保证功能完整性。
        // throw "Attempt to execute code removed by Dart AOT compiler (TFA)";
        return response;
      });

      // 异步注册网络服务
      // await Get.putAsync<NetworkService>(() async => await NetworkService().init());

      // 添加硬件键盘处理器
      HardwareKeyboard.instance.addHandler((KeyEvent event) {
        return event is KeyDownEvent &&
            HardwareKeyboard.instance.physicalKeysPressed.contains(
              event.physicalKey,
            );
      });

      // 注册SharedPreferences实例
      Get.put<SharedPreferences>(prefs);
      logger.e('--------------------------------------------------------7');
      // 运行App
      runApp(
        ScreenUtilInit(
          designSize: const Size(375, 812), // UI设计稿尺寸
          minTextAdapt: true, // 根据宽度和高度同时缩放文本
          splitScreenMode: true, // 支持分屏模式
          builder: (context, child) {
            // GetMaterialApp 的 builder
            final mediaQueryData = MediaQuery.of(context);
            ScreenUtil.init(context, designSize: const Size(375, 812));
            return MediaQuery(
              data: mediaQueryData.copyWith(
                textScaler: TextScaler.linear(1.0), // 禁止字体大小随系统设置变化
              ),
              child: child!,
            );
          },
          child: GetMaterialApp(
            title: 'hidance',
            initialRoute: initialRoute,
            getPages: AppPages.routes,
            locale: const Locale('zh', 'CN'),
            unknownRoute: GetPage(
              name: '/notfound',
              page: () => Scaffold(
                body: Center(child: Text('页面未找到: ${Get.currentRoute}')),
              ),
            ),
            navigatorObservers: [
              Get.put(RouteObserver<PageRoute>()),
              CustomNavigatorObserver(),
            ],
            builder: (context, child) {
              return MediaQuery(
                data: MediaQuery.of(
                  context,
                ).copyWith(textScaler: TextScaler.linear(1.0)),
                child: child ?? Container(), // 如果child为空，提供一个备用widget
              );
            },
            theme: ThemeData(
              primarySwatch: Colors.blue,
              appBarTheme: const AppBarTheme(),
              visualDensity: VisualDensity.adaptivePlatformDensity,
            ),
            themeMode: ThemeMode.light,
            debugShowCheckedModeBanner: false,
            defaultTransition: Transition.rightToLeft,
            smartManagement: SmartManagement.full,
          ),
        ),
      );
    } catch (e, s) {
      logger.e('应用初始化失败: $e', stackTrace: s);
      // 即使初始化失败，也尝试运行一个备用UI
      runApp(
        MaterialApp(
          home: Scaffold(body: Center(child: Text('应用初始化失败: $e'))),
        ),
      );
    }
  }

  // static Future<String> _getChannelName() async {
  //   try {
  //     String channelName = await ChannelUtil.getChannelName();
  //     SharedPreferences prefs = await SharedPreferences.getInstance();
  //     await prefs.setString('analytics_app_channel', channelName);
  //     logger.d('渠道信息已同步: $channelName');
  //     return channelName;
  //   } catch (e, s) {
  //     logger.e('获取渠道信息失败: $e');
  //     return 'unknown';
  //   }
  // }

  static void initDependencies() {
    final logger = CustomApp.logger;
    try {
      logger.d("开始初始化服务...");

      Get.putAsync<NetworkService>(() async => await NetworkService().init());

      // Get.put(ErrorClassificationService(logger: Logger()), permanent: true);
      // logger.d("ErrorClassificationService 初始化完成");

      // Get.lazyPut<UserInfoService>(() => UserInfoService(), fenix: true);

      if (!Get.isRegistered<GlobalImportStateManager>()) {
        Get.put(GlobalImportStateManager(), permanent: true);
        logger.d("GlobalImportStateManager 初始化完成");
      }

      if (!Get.isRegistered<VideoImportRecordService>()) {
        Get.put(VideoImportRecordService(), permanent: true);
        logger.d("VideoImportRecordService 初始化完成");
      }

      if (!Get.isRegistered<LocalDatabaseService>()) {
        Get.put(LocalDatabaseService(), permanent: true);
        logger.d("LocalDatabaseService 初始化完成");
      }

      if (!Get.isRegistered<LocalVideoScoreService>()) {
        Get.put(LocalVideoScoreService(), permanent: true);
        logger.d("LocalVideoScoreService 初始化完成");
      }

      if (!Get.isRegistered<ImageOptimizationService>()) {
        Get.put(ImageOptimizationService(), permanent: true);
        logger.d("ImageOptimizationService 初始化完成");
      }

      if (!Get.isRegistered<MyWorksService>()) {
        Get.put(MyWorksService(), permanent: true);
        logger.d("MyWorksService 初始化完成");
      }

      if (!Get.isRegistered<DataSyncService>()) {
        Get.put(DataSyncService(), permanent: true);
        logger.d("DataSyncService 初始化完成");
      }

      if (!Get.isRegistered<CreationPreloadService>()) {
        try {
          Get.put(CreationPreloadService(), permanent: true);
          logger.d("CreationPreloadService 初始化完成");
        } catch (e, s) {
          logger.e("CreationPreloadService 初始化失败: $e", stackTrace: s);
          // 即使失败也要注册一个空的服务实例
          Get.put(CreationPreloadService(), permanent: true);
        }
      }

      Get.lazyPut<MyWorksController>(() => MyWorksController(), fenix: true);
      logger.d("MyWorksController 配置为懒加载模式");

      // 注册CreationController
      if (!Get.isRegistered<CreationController>()) {
        Get.lazyPut<CreationController>(
          () => CreationController(),
          fenix: true,
        );
        logger.d("CreationController 配置为懒加载模式");
      }

      // 注册HomeController，因为CreationView可能需要它
      if (!Get.isRegistered<HomeController>()) {
        Get.lazyPut<HomeController>(() => HomeController(), fenix: true);
        logger.d("HomeController 配置为懒加载模式");
      }

      logger.d("所有服务初始化完成");
    } catch (error, stackTrace) {
      logger.e("服务初始化失败: $error", stackTrace: stackTrace);
    }
  }
}

class CustomNavigatorObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    final routeName = route.settings.name;
    if (routeName != null && routeName == '/main') {
      // 原始汇编中有一个比较，但后续没有特殊逻辑。
      // 这里可以根据需要添加逻辑
    }
    CustomApp.logger.i('页面跳转: ${route.settings.name}');
  }
}
