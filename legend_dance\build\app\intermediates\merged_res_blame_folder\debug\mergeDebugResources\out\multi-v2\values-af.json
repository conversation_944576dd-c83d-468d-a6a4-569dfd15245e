{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3411,3509,3611,3709,3807,3914,4023,8445", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "3504,3606,3704,3802,3909,4018,4138,8541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "739,847,943,1049,1134,1237,1355,1432,1508,1599,1692,1787,1881,1980,2073,2168,2267,2362,2456,2537,2644,2749,2846,2954,3057,3159,3313,8364", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "842,938,1044,1129,1232,1350,1427,1503,1594,1687,1782,1876,1975,2068,2163,2262,2357,2451,2532,2639,2744,2841,2949,3052,3154,3308,3406,8440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,345,484,653,732", "endColumns": "70,87,80,138,168,78,76", "endOffsets": "171,259,340,479,648,727,804"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4143,8056,8144,8225,8546,8715,8794", "endColumns": "70,87,80,138,168,78,76", "endOffsets": "4209,8139,8220,8359,8710,8789,8866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,500,689,776,864,944,1031,1117,1188,1255,1353,1446,1516,1580,1642,1711,1789,1866,1942,2014,2096,2170,2236,2315,2394,2457,2522,2575,2633,2681,2742,2807,2879,2944,3012,3070,3128,3194,3258,3324,3376,3435,3508,3581", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,77,76,75,71,81,73,65,78,78,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "281,495,684,771,859,939,1026,1112,1183,1250,1348,1441,1511,1575,1637,1706,1784,1861,1937,2009,2091,2165,2231,2310,2389,2452,2517,2570,2628,2676,2737,2802,2874,2939,3007,3065,3123,3189,3253,3319,3371,3430,3503,3576,3631"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,550,4214,4301,4389,4469,4556,4642,4713,4780,4878,4971,5041,5105,5167,5236,5314,5391,5467,5539,5621,5695,5761,5840,5919,5982,6699,6995,7053,7101,7162,7227,7299,7364,7432,7490,7548,7614,7678,7744,7796,7855,7928,8001", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,77,76,75,71,81,73,65,78,78,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "331,545,734,4296,4384,4464,4551,4637,4708,4775,4873,4966,5036,5100,5162,5231,5309,5386,5462,5534,5616,5690,5756,5835,5914,5977,6042,6747,7048,7096,7157,7222,7294,7359,7427,7485,7543,7609,7673,7739,7791,7850,7923,7996,8051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,184,250,317,392,462,551,635", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "119,179,245,312,387,457,546,630,702"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6047,6116,6176,6242,6309,6384,6454,6543,6627", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "6111,6171,6237,6304,6379,6449,6538,6622,6694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,137,218", "endColumns": "81,80,79", "endOffsets": "132,213,293"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6752,6834,6915", "endColumns": "81,80,79", "endOffsets": "6829,6910,6990"}}]}]}