{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "48,49,50,51,52,53,54,114", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3648,3746,3848,3948,4047,4149,4258,8835", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3741,3843,3943,4042,4144,4253,4370,8931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,939,1028,1116,1212,1308,1383,1449,1548,1645,1716,1781,1844,1913,1998,2083,2161,2237,2317,2386,2462,2556,2646,2711,2774,2827,2885,2933,2994,3058,3128,3193,3262,3323,3381,3447,3511,3577,3629,3691,3767,3843", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,84,84,77,75,79,68,75,93,89,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "281,577,847,934,1023,1111,1207,1303,1378,1444,1543,1640,1711,1776,1839,1908,1993,2078,2156,2232,2312,2381,2457,2551,2641,2706,2769,2822,2880,2928,2989,3053,3123,3188,3257,3318,3376,3442,3506,3572,3624,3686,3762,3838,3895"}, "to": {"startLines": "2,11,16,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,632,4446,4533,4622,4710,4806,4902,4977,5043,5142,5239,5310,5375,5438,5507,5592,5677,5755,5831,5911,5980,6056,6150,6240,6305,7065,7378,7436,7484,7545,7609,7679,7744,7813,7874,7932,7998,8062,8128,8180,8242,8318,8394", "endLines": "10,15,20,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,84,84,77,75,79,68,75,93,89,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "331,627,897,4528,4617,4705,4801,4897,4972,5038,5137,5234,5305,5370,5433,5502,5587,5672,5750,5826,5906,5975,6051,6145,6235,6300,6363,7113,7431,7479,7540,7604,7674,7739,7808,7869,7927,7993,8057,8123,8175,8237,8313,8389,8446"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "902,1023,1127,1240,1324,1428,1549,1634,1714,1805,1898,1993,2087,2187,2280,2375,2469,2560,2652,2735,2847,2955,3055,3169,3275,3381,3545,8751", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "1018,1122,1235,1319,1423,1544,1629,1709,1800,1893,1988,2082,2182,2275,2370,2464,2555,2647,2730,2842,2950,3050,3164,3270,3376,3540,3643,8830"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,144,233", "endColumns": "88,88,81", "endOffsets": "139,228,310"}, "to": {"startLines": "90,91,92", "startColumns": "4,4,4", "startOffsets": "7118,7207,7296", "endColumns": "88,88,81", "endOffsets": "7202,7291,7373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,265,337,415,495,585,678", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "131,195,260,332,410,490,580,673,747"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6368,6449,6513,6578,6650,6728,6808,6898,6991", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "6444,6508,6573,6645,6723,6803,6893,6986,7060"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,337,476,645,732", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "171,257,332,471,640,727,808"}, "to": {"startLines": "55,110,111,112,115,116,117", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4375,8451,8537,8612,8936,9105,9192", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "4441,8532,8607,8746,9100,9187,9268"}}]}]}