"-Xallow-no-source-files" "-classpath" "E:\\ai-dance\\ai-dance-flutter\\legend_dance\\build\\camera_android_camerax\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e0526409eb542938121c92c37b78c485\\transformed\\jetified-camera-video-1.5.0-beta01-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c5916a58534f450eb767eb4e8e14245e\\transformed\\jetified-camera-lifecycle-1.5.0-beta01-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9addc35db80dc0e5bf6ac4e7ab0b1f72\\transformed\\jetified-camera-camera2-1.5.0-beta01-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\96b37a4f537511c118bcbebd31caa02e\\transformed\\jetified-camera-core-1.5.0-beta01-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c22940fd007c288ac2172fb605aed0f1\\transformed\\jetified-flutter_embedding_debug-1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f0c43bb5fcbf5d527acc938eed5c5ee7\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fb1a370ddc9b6f09e36542d47d36296d\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df5d28f461ffd56f36cdd72acdfc344f\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d6f4d647e17b29c5423a0c7fa8c98f2d\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\115b656e1a4aecf880260d6d83623996\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c315d47e2f7879c3deeb8a2382af17bc\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ae7f7f186029d473ac3dbfcddcd85b22\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\890fa23f232b0e27acd86381ae22e986\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\496b2ffad9f48ea28eeff97372eecba8\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0718b469eda36dc9592c0463fc44f0f5\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\aaa838aa6779c7b3265d9262098da713\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\15ba965be5d36f1409c507c4f4f01060\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8072e09447510b0de763fa662fba307a\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4fd0b64dbcc747184e3367538d3940e0\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fff59de3233a53a251731adb4c6ced0a\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ca08bea8da6ce44b915b59db148eb4a9\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\93c365926788afce780b435eb07fb8b6\\transformed\\jetified-annotation-experimental-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\546c29fc3b12ba0ada2deef99fc20985\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f1860f1dedc07b8c1db1817385fde25\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\82f50a1147ea7962ad05f59f739641fb\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c2ee8186f9be5e85ee542a28b03bfd36\\transformed\\jetified-annotation-jvm-1.8.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e789c606285d17c2ec99c03f43d33c98\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7ead9a6c3bb927c55881788d3824402d\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7a963a3f078233bcb9f6b8b5c76988b4\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9f528df4bae2341bfd26279a570b8663\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ade16677ec891dea4619d807ecb70bab\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9fd9f0b4ef52c04973f5d6766b25135d\\transformed\\jetified-guava-33.4.0-android.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a1a24e07e8946987328cdbf73d5c0d1c\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\51357615e5825f038c2637fd58fd16d8\\transformed\\jetified-listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cd60595a499747cfa90d326929eabb59\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e2cbe243dd93764f3ea77e6c23ccd98f\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e9b80c786d1f3bf9007353926f4b6eb1\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c9ecfd31e63352a0903fa48c2568b63a\\transformed\\jetified-jspecify-1.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\39f1adcf900dd896204d18466457c3b3\\transformed\\jetified-failureaccess-1.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\134aa3b0545c6ee5e19a84ccdd0d8d78\\transformed\\jetified-jsr305-3.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2f69bd6e8e4e636c90cad376251e8730\\transformed\\jetified-checker-qual-3.43.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7d024dc9c2a7ad03e7f4a9b21823c6e0\\transformed\\jetified-error_prone_annotations-2.36.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cc06aeaada29b792233eab0fbc60e0f9\\transformed\\jetified-j2objc-annotations-3.0.0.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-35\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "E:\\ai-dance\\ai-dance-flutter\\legend_dance\\build\\camera_android_camerax\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "camera_android_camerax_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\AnalyzerProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\AspectRatioStrategyProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\Camera2CameraControlProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\Camera2CameraInfoProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraAndroidCameraxPlugin.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraCharacteristicsProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraControlProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraInfoProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraIntegerRangeProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraPermissionsError.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraPermissionsErrorProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraPermissionsManager.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraSelectorProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraSizeProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraStateProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraStateStateErrorProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CaptureRequestOptionsProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CaptureRequestProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\DeviceOrientationManager.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\DeviceOrientationManagerProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\DisplayOrientedMeteringPointFactoryProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ExposureStateProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\FallbackStrategyProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\FocusMeteringActionBuilderProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\FocusMeteringActionProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\FocusMeteringResultProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\GeneratedCameraXLibrary.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ImageAnalysisProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ImageCaptureProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ImageProxyProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\LiveDataProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\MeteringPointFactoryProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\MeteringPointProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ObserverProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\PendingRecordingProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\PlaneProxyProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\PreviewProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ProcessCameraProviderProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ProxyApiRegistrar.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ProxyLifecycleProvider.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\QualitySelectorProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\RecorderProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\RecordingProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ResolutionFilterProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ResolutionInfoProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ResolutionSelectorProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ResolutionStrategyProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\SystemServicesManager.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\SystemServicesManagerProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\VideoCaptureProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\VideoRecordEventListener.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\VideoRecordEventListenerProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ZoomStateProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraXLibrary.g.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\camera_android_camerax-0.6.19+1\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ResultCompat.kt"