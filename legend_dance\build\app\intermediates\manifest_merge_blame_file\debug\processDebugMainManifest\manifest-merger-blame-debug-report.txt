1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.legend_dance"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->E:\ai-dance\ai-dance-flutter\legend_dance\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->E:\ai-dance\ai-dance-flutter\legend_dance\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->E:\ai-dance\ai-dance-flutter\legend_dance\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->E:\ai-dance\ai-dance-flutter\legend_dance\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->E:\ai-dance\ai-dance-flutter\legend_dance\android\app\src\main\AndroidManifest.xml:41:13-72
25-->E:\ai-dance\ai-dance-flutter\legend_dance\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->E:\ai-dance\ai-dance-flutter\legend_dance\android\app\src\main\AndroidManifest.xml:42:13-50
27-->E:\ai-dance\ai-dance-flutter\legend_dance\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29
30        <package android:name="com.tencent.mm" />
30-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-50
30-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:18-47
31    </queries>
32
33    <uses-feature android:name="android.hardware.camera.any" />
33-->[:camera_android_camerax] E:\ai-dance\ai-dance-flutter\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-64
33-->[:camera_android_camerax] E:\ai-dance\ai-dance-flutter\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:19-61
34
35    <uses-permission android:name="android.permission.CAMERA" />
35-->[:camera_android_camerax] E:\ai-dance\ai-dance-flutter\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-65
35-->[:camera_android_camerax] E:\ai-dance\ai-dance-flutter\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-62
36    <uses-permission android:name="android.permission.RECORD_AUDIO" />
36-->[:camera_android_camerax] E:\ai-dance\ai-dance-flutter\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-71
36-->[:camera_android_camerax] E:\ai-dance\ai-dance-flutter\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-68
37    <uses-permission
37-->[:camera_android_camerax] E:\ai-dance\ai-dance-flutter\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-13:38
38        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
38-->[:camera_android_camerax] E:\ai-dance\ai-dance-flutter\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-65
39        android:maxSdkVersion="28" />
39-->[:camera_android_camerax] E:\ai-dance\ai-dance-flutter\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-35
40    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
41    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
41-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
41-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-76
42    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
42-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-76
42-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-73
43    <uses-permission android:name="android.permission.WAKE_LOCK" />
43-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
43-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:22-65
44    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
44-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
44-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:22-78
45    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
45-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
45-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
46
47    <permission
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
48        android:name="com.example.legend_dance.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.example.legend_dance.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
52
53    <application
54        android:name="android.app.Application"
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
56        android:debuggable="true"
57        android:extractNativeLibs="false"
58        android:icon="@mipmap/ic_launcher"
59        android:label="legend_dance" >
60        <activity
61            android:name="com.example.legend_dance.MainActivity"
62            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
63            android:exported="true"
64            android:hardwareAccelerated="true"
65            android:launchMode="singleTop"
66            android:taskAffinity=""
67            android:theme="@style/LaunchTheme"
68            android:windowSoftInputMode="adjustResize" >
69
70            <!--
71                 Specifies an Android theme to apply to this Activity as soon as
72                 the Android process has started. This theme is visible to the user
73                 while the Flutter UI initializes. After that, this theme continues
74                 to determine the Window background behind the Flutter UI.
75            -->
76            <meta-data
77                android:name="io.flutter.embedding.android.NormalTheme"
78                android:resource="@style/NormalTheme" />
79
80            <intent-filter>
81                <action android:name="android.intent.action.MAIN" />
82
83                <category android:name="android.intent.category.LAUNCHER" />
84            </intent-filter>
85        </activity>
86        <!--
87             Don't delete the meta-data below.
88             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
89        -->
90        <meta-data
91            android:name="flutterEmbedding"
92            android:value="2" />
93
94        <activity
94-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:9-22:58
95            android:name="com.jarvan.fluwx.wxapi.FluwxWXEntryActivity"
95-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-71
96            android:exported="false"
96-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-37
97            android:launchMode="singleTask"
97-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-44
98            android:taskAffinity="com.example.legend_dance"
98-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-52
99            android:theme="@style/DisablePreviewTheme" />
99-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-55
100
101        <activity-alias
101-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:9-36:58
102            android:name="com.example.legend_dance.wxapi.WXEntryActivity"
102-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-66
103            android:exported="true"
103-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-36
104            android:launchMode="singleTop"
104-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-43
105            android:targetActivity="com.jarvan.fluwx.wxapi.FluwxWXEntryActivity"
105-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-81
106            android:taskAffinity="com.example.legend_dance"
106-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-52
107            android:theme="@style/DisablePreviewTheme" />
107-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-55
108        <activity-alias
108-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:9-29:58
109            android:name="com.example.legend_dance.wxapi.WXPayEntryActivity"
109-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-69
110            android:exported="true"
110-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-36
111            android:launchMode="singleInstance"
111-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-48
112            android:targetActivity="com.jarvan.fluwx.wxapi.FluwxWXEntryActivity"
112-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-81
113            android:theme="@style/DisablePreviewTheme" />
113-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-55
114
115        <provider
115-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-46:20
116            android:name="com.jarvan.fluwx.FluwxFileProvider"
116-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-62
117            android:authorities="com.example.legend_dance.fluwxprovider"
117-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-65
118            android:exported="false"
118-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
119            android:grantUriPermissions="true" >
119-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-47
120            <meta-data
120-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-45:69
121                android:name="android.support.FILE_PROVIDER_PATHS"
121-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:17-67
122                android:resource="@xml/fluwx_file_provider_paths" />
122-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:17-66
123        </provider>
124        <!--
125           Declares a provider which allows us to store files to share in
126           '.../caches/share_plus' and grant the receiving action access
127        -->
128        <provider
128-->[:share_plus] E:\ai-dance\ai-dance-flutter\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
129            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
129-->[:share_plus] E:\ai-dance\ai-dance-flutter\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
130            android:authorities="com.example.legend_dance.flutter.share_provider"
130-->[:share_plus] E:\ai-dance\ai-dance-flutter\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
131            android:exported="false"
131-->[:share_plus] E:\ai-dance\ai-dance-flutter\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
132            android:grantUriPermissions="true" >
132-->[:share_plus] E:\ai-dance\ai-dance-flutter\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
133            <meta-data
133-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-45:69
134                android:name="android.support.FILE_PROVIDER_PATHS"
134-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:17-67
135                android:resource="@xml/flutter_share_file_paths" />
135-->[:fluwx] E:\ai-dance\ai-dance-flutter\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:17-66
136        </provider>
137        <!--
138           This manifest declared broadcast receiver allows us to use an explicit
139           Intent when creating a PendingItent to be informed of the user's choice
140        -->
141        <receiver
141-->[:share_plus] E:\ai-dance\ai-dance-flutter\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
142            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
142-->[:share_plus] E:\ai-dance\ai-dance-flutter\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
143            android:exported="false" >
143-->[:share_plus] E:\ai-dance\ai-dance-flutter\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
144            <intent-filter>
144-->[:share_plus] E:\ai-dance\ai-dance-flutter\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
145                <action android:name="EXTRA_CHOSEN_COMPONENT" />
145-->[:share_plus] E:\ai-dance\ai-dance-flutter\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
145-->[:share_plus] E:\ai-dance\ai-dance-flutter\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
146            </intent-filter>
147        </receiver>
148
149        <service
149-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:24:9-33:19
150            android:name="androidx.camera.core.impl.MetadataHolderService"
150-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:25:13-75
151            android:enabled="false"
151-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:26:13-36
152            android:exported="false" >
152-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:27:13-37
153            <meta-data
153-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:30:13-32:89
154                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
154-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:31:17-103
155                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
155-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:32:17-86
156        </service>
157
158        <provider
158-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
159            android:name="androidx.startup.InitializationProvider"
159-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
160            android:authorities="com.example.legend_dance.androidx-startup"
160-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
161            android:exported="false" >
161-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
162            <meta-data
162-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
163                android:name="androidx.work.WorkManagerInitializer"
163-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
164                android:value="androidx.startup" />
164-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
165            <meta-data
165-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
166                android:name="androidx.emoji2.text.EmojiCompatInitializer"
166-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
167                android:value="androidx.startup" />
167-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
168            <meta-data
168-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
169                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
169-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
170                android:value="androidx.startup" />
170-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
171            <meta-data
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
172                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
173                android:value="androidx.startup" />
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
174        </provider>
175
176        <service
176-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
177            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
177-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
178            android:directBootAware="false"
178-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
179            android:enabled="@bool/enable_system_alarm_service_default"
179-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
180            android:exported="false" />
180-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
181        <service
181-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
182            android:name="androidx.work.impl.background.systemjob.SystemJobService"
182-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
183            android:directBootAware="false"
183-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
184            android:enabled="@bool/enable_system_job_service_default"
184-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
185            android:exported="true"
185-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
186            android:permission="android.permission.BIND_JOB_SERVICE" />
186-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
187        <service
187-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
188            android:name="androidx.work.impl.foreground.SystemForegroundService"
188-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
189            android:directBootAware="false"
189-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
190            android:enabled="@bool/enable_system_foreground_service_default"
190-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
191            android:exported="false" />
191-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
192
193        <receiver
193-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
194            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
194-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
195            android:directBootAware="false"
195-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
196            android:enabled="true"
196-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
197            android:exported="false" />
197-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
198        <receiver
198-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
199            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
199-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
200            android:directBootAware="false"
200-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
201            android:enabled="false"
201-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
202            android:exported="false" >
202-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
203            <intent-filter>
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
204                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
205                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
205-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
205-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
206            </intent-filter>
207        </receiver>
208        <receiver
208-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
209            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
209-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
210            android:directBootAware="false"
210-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
211            android:enabled="false"
211-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
212            android:exported="false" >
212-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
213            <intent-filter>
213-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
214                <action android:name="android.intent.action.BATTERY_OKAY" />
214-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
214-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
215                <action android:name="android.intent.action.BATTERY_LOW" />
215-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
215-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
216            </intent-filter>
217        </receiver>
218        <receiver
218-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
219            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
219-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
220            android:directBootAware="false"
220-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
221            android:enabled="false"
221-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
222            android:exported="false" >
222-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
223            <intent-filter>
223-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
224                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
224-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
224-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
225                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
225-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
225-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
226            </intent-filter>
227        </receiver>
228        <receiver
228-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
229            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
229-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
230            android:directBootAware="false"
230-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
231            android:enabled="false"
231-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
232            android:exported="false" >
232-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
233            <intent-filter>
233-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
234                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
234-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
234-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
235            </intent-filter>
236        </receiver>
237        <receiver
237-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
238            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
238-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
239            android:directBootAware="false"
239-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
240            android:enabled="false"
240-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
241            android:exported="false" >
241-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
242            <intent-filter>
242-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
243                <action android:name="android.intent.action.BOOT_COMPLETED" />
243-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
243-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
244                <action android:name="android.intent.action.TIME_SET" />
244-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
244-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
245                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
245-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
245-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
246            </intent-filter>
247        </receiver>
248        <receiver
248-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
249            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
249-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
250            android:directBootAware="false"
250-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
251            android:enabled="@bool/enable_system_alarm_service_default"
251-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
252            android:exported="false" >
252-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
253            <intent-filter>
253-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
254                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
254-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
254-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
255            </intent-filter>
256        </receiver>
257        <receiver
257-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
258            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
258-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
259            android:directBootAware="false"
259-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
260            android:enabled="true"
260-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
261            android:exported="true"
261-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
262            android:permission="android.permission.DUMP" >
262-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
263            <intent-filter>
263-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
264                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
264-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
264-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\767073f2918b1489969b160526c3ffbf\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
265            </intent-filter>
266        </receiver>
267
268        <uses-library
268-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
269            android:name="androidx.window.extensions"
269-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
270            android:required="false" />
270-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
271        <uses-library
271-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
272            android:name="androidx.window.sidecar"
272-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
273            android:required="false" />
273-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
274
275        <receiver
275-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
276            android:name="androidx.profileinstaller.ProfileInstallReceiver"
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
277            android:directBootAware="false"
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
278            android:enabled="true"
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
279            android:exported="true"
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
280            android:permission="android.permission.DUMP" >
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
281            <intent-filter>
281-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
282                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
282-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
282-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
283            </intent-filter>
284            <intent-filter>
284-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
285                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
285-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
285-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
286            </intent-filter>
287            <intent-filter>
287-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
288                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
288-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
288-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
289            </intent-filter>
290            <intent-filter>
290-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
291                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
291-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
291-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
292            </intent-filter>
293        </receiver>
294
295        <service
295-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0d21def952d905d9a89c82bf30f53ec\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
296            android:name="androidx.room.MultiInstanceInvalidationService"
296-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0d21def952d905d9a89c82bf30f53ec\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
297            android:directBootAware="true"
297-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0d21def952d905d9a89c82bf30f53ec\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
298            android:exported="false" />
298-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0d21def952d905d9a89c82bf30f53ec\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
299        <service
299-->[com.google.android.datatransport:transport-backend-cct:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70728dc7fd66d07706387bf9e9879d14\transformed\jetified-transport-backend-cct-3.1.0\AndroidManifest.xml:29:9-35:19
300            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
300-->[com.google.android.datatransport:transport-backend-cct:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70728dc7fd66d07706387bf9e9879d14\transformed\jetified-transport-backend-cct-3.1.0\AndroidManifest.xml:30:13-103
301            android:exported="false" >
301-->[com.google.android.datatransport:transport-backend-cct:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70728dc7fd66d07706387bf9e9879d14\transformed\jetified-transport-backend-cct-3.1.0\AndroidManifest.xml:31:13-37
302            <meta-data
302-->[com.google.android.datatransport:transport-backend-cct:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70728dc7fd66d07706387bf9e9879d14\transformed\jetified-transport-backend-cct-3.1.0\AndroidManifest.xml:32:13-34:39
303                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
303-->[com.google.android.datatransport:transport-backend-cct:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70728dc7fd66d07706387bf9e9879d14\transformed\jetified-transport-backend-cct-3.1.0\AndroidManifest.xml:33:17-94
304                android:value="cct" />
304-->[com.google.android.datatransport:transport-backend-cct:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70728dc7fd66d07706387bf9e9879d14\transformed\jetified-transport-backend-cct-3.1.0\AndroidManifest.xml:34:17-36
305        </service>
306        <service
306-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:26:9-30:19
307            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
307-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:27:13-117
308            android:exported="false"
308-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:28:13-37
309            android:permission="android.permission.BIND_JOB_SERVICE" >
309-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:29:13-69
310        </service>
311
312        <receiver
312-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:32:9-34:40
313            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
313-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:33:13-132
314            android:exported="false" />
314-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:34:13-37
315    </application>
316
317</manifest>
