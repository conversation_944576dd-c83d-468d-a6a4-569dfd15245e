1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.jarvan.fluwx" >
4
5    <uses-sdk android:minSdkVersion="19" />
6
7    <uses-permission android:name="android.permission.INTERNET" />
7-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:3:5-67
7-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:3:22-64
8    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
8-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:4:5-79
8-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:4:22-76
9    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
9-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:5:5-76
9-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:5:22-73
10
11    <!-- Support WeChat query on Android P -->
12    <queries>
12-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:8:5-10:15
13        <package android:name="com.tencent.mm" />
13-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:9:9-50
13-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:9:18-47
14    </queries>
15
16    <application>
16-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:12:5-45:19
17        <activity
17-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:14:9-19:58
18            android:name="com.jarvan.fluwx.wxapi.FluwxWXEntryActivity"
18-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:15:13-55
19            android:exported="false"
19-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:16:13-37
20            android:launchMode="singleTask"
20-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:17:13-44
21            android:taskAffinity="${applicationId}"
21-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:18:13-52
22            android:theme="@style/DisablePreviewTheme" />
22-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:19:13-55
23
24        <activity-alias
24-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:29:9-34:58
25            android:name="${applicationId}.wxapi.WXPayEntryActivity"
25-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:30:13-69
26            android:exported="true"
26-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:31:13-36
27            android:launchMode="singleInstance"
27-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:32:13-48
28            android:targetActivity="com.jarvan.fluwx.wxapi.FluwxWXEntryActivity"
28-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:33:13-81
29            android:theme="@style/DisablePreviewTheme" />
29-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:34:13-55
30        <activity-alias
30-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:21:9-27:58
31            android:name="${applicationId}.wxapi.WXEntryActivity"
31-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:22:13-66
32            android:exported="true"
32-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:23:13-36
33            android:launchMode="singleTop"
33-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:24:13-43
34            android:targetActivity="com.jarvan.fluwx.wxapi.FluwxWXEntryActivity"
34-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:25:13-81
35            android:taskAffinity="${applicationId}"
35-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:26:13-52
36            android:theme="@style/DisablePreviewTheme" />
36-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:27:13-55
37
38        <provider
38-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:36:9-44:20
39            android:name="com.jarvan.fluwx.FluwxFileProvider"
39-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:37:13-62
40            android:authorities="${applicationId}.fluwxprovider"
40-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:38:13-65
41            android:exported="false"
41-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:39:13-37
42            android:grantUriPermissions="true" >
42-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:40:13-47
43            <meta-data
43-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:41:13-43:69
44                android:name="android.support.FILE_PROVIDER_PATHS"
44-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:42:17-67
45                android:resource="@xml/fluwx_file_provider_paths" />
45-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\fluwx-5.7.2\android\src\main\AndroidManifest.xml:43:17-66
46        </provider>
47    </application>
48
49</manifest>
