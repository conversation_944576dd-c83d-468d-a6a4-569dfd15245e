{"logs": [{"outputFile": "com.example.legend_dance.app-mergeDebugResources-52:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "46,47,48,49,50,51,52,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3418,3519,3621,3724,3828,3929,4034,8500", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "3514,3616,3719,3823,3924,4029,4140,8596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0411a5e6a731895377ae1714cfd7fbbc\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,476,660,748,837,914,1006,1094,1170,1234,1325,1416,1481,1546,1608,1676,1760,1848,1930,2001,2082,2152,2228,2315,2399,2468,2534,2587,2645,2693,2754,2818,2890,2949,3012,3075,3135,3201,3265,3331,3383,3441,3513,3585", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,83,87,81,70,80,69,75,86,83,68,65,52,57,47,60,63,71,58,62,62,59,65,63,65,51,57,71,71,53", "endOffsets": "280,471,655,743,832,909,1001,1089,1165,1229,1320,1411,1476,1541,1603,1671,1755,1843,1925,1996,2077,2147,2223,2310,2394,2463,2529,2582,2640,2688,2749,2813,2885,2944,3007,3070,3130,3196,3260,3326,3378,3436,3508,3580,3634"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,526,4219,4307,4396,4473,4565,4653,4729,4793,4884,4975,5040,5105,5167,5235,5319,5407,5489,5560,5641,5711,5787,5874,5958,6027,6750,7069,7127,7175,7236,7300,7372,7431,7494,7557,7617,7683,7747,7813,7865,7923,7995,8067", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,83,87,81,70,80,69,75,86,83,68,65,52,57,47,60,63,71,58,62,62,59,65,63,65,51,57,71,71,53", "endOffsets": "330,521,705,4302,4391,4468,4560,4648,4724,4788,4879,4970,5035,5100,5162,5230,5314,5402,5484,5555,5636,5706,5782,5869,5953,6022,6088,6798,7122,7170,7231,7295,7367,7426,7489,7552,7612,7678,7742,7808,7860,7918,7990,8062,8116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c8a9aa93dc7d5cfe9b778478c6b03c\\transformed\\appcompat-1.6.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "710,820,921,1031,1119,1226,1340,1422,1500,1591,1684,1778,1877,1977,2070,2165,2259,2350,2442,2527,2632,2738,2838,2947,3052,3154,3312,8416", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "815,916,1026,1114,1221,1335,1417,1495,1586,1679,1773,1872,1972,2065,2160,2254,2345,2437,2522,2627,2733,2833,2942,3047,3149,3307,3413,8495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cd999778df0eff0ea77ca81198ac0cd\\transformed\\jetified-extension-mediasession-2.17.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,235", "endColumns": "87,91,85", "endOffsets": "138,230,316"}, "to": {"startLines": "88,89,90", "startColumns": "4,4,4", "startOffsets": "6803,6891,6983", "endColumns": "87,91,85", "endOffsets": "6886,6978,7064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5af001820bc85f78408ceb514ca0111a\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,249,314,392,459,548,641", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "120,181,244,309,387,454,543,636,707"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6093,6163,6224,6287,6352,6430,6497,6586,6679", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "6158,6219,6282,6347,6425,6492,6581,6674,6745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,263,342,474,643,727", "endColumns": "73,83,78,131,168,83,78", "endOffsets": "174,258,337,469,638,722,801"}, "to": {"startLines": "53,108,109,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4145,8121,8205,8284,8601,8770,8854", "endColumns": "73,83,78,131,168,83,78", "endOffsets": "4214,8200,8279,8411,8765,8849,8928"}}]}]}